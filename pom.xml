<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.boomi.services</groupId>
        <artifactId>service-parent</artifactId>
        <version>4.6.29</version>
    </parent>

    <groupId>com.boomi.aiagentregistry</groupId>
    <artifactId>ai-agent-registry-service</artifactId>
    <packaging>jar</packaging>
    <name>AI Agent Registry Service</name>
    <version>1.0.6-SNAPSHOT</version>

    <properties>
        <jdk.source>17</jdk.source>
        <jdk.target>17</jdk.target>
        <netty.version>4.1.101.Final</netty.version>
        <newrelic-agent.version>8.3.0</newrelic-agent.version>
        <junit.version>5.9.2</junit.version>
        <graphqlmodel.version>5.0.207</graphqlmodel.version>
        <testcontainers.version>1.19.7</testcontainers.version>
        <boomi-dependencies-pom.version>4.4.2</boomi-dependencies-pom.version>
        <boomi-platform-services-common.version>1.18.0</boomi-platform-services-common.version>
        <boomi-api-common.version>1.1.4</boomi-api-common.version>
        <aws-postgresql-jdbc.version>0.1.0</aws-postgresql-jdbc.version>
        <postgres.version>42.7.4</postgres.version>
        <boomi.newrelic.forwardLogs>true</boomi.newrelic.forwardLogs>
        <json.smart.version>2.4.11</json.smart.version>
        <hypersistence.version>3.2.0</hypersistence.version>
        <json.path.version>2.9.0</json.path.version>
        <skipTests>false</skipTests>
        <skipIT>false</skipIT>
        <io.hypersistence.hibernate.version>3.8.2</io.hypersistence.hibernate.version>
        <skipUTs>${skipTests}</skipUTs>
        <exclude.copy.list>javax.servlet-api,javax.activation,jakarta.validation-api,graphql-java-tools,common-lite,
            spring-jcl,commons-lang3,federation-graphql-java-support
        </exclude.copy.list>
        <minimized-jvm.java.modules>java.base,java.desktop,java.instrument,java.naming,java.prefs,java.rmi,
            java.scripting,java.security.jgss,java.sql,jdk.httpserver,jdk.jfr,jdk.management,jdk.unsupported
        </minimized-jvm.java.modules>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <lombok.version>1.18.30</lombok.version>
        <boomi.util.version>3.0.1</boomi.util.version>
        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
        <lombok.mapstruct.binding.version>0.2.0</lombok.mapstruct.binding.version>
        <org.liquibase.version>4.25.0</org.liquibase.version>
        <org.liquibase.maven.plugin.version>4.3.1</org.liquibase.maven.plugin.version>
        <project.db.changelogs.location>/classes/db.changelog</project.db.changelogs.location>
        <okhttp.version>4.12.0</okhttp.version>
        <netty-common.version>4.1.118.Final</netty-common.version>
        <netty-handler.version>4.1.118.Final</netty-handler.version>
        <protobuf-java.version>3.25.5</protobuf-java.version>
        <spring-security-config.version>6.2.8</spring-security-config.version>
        <spring-security-core.version>6.2.8</spring-security-core.version>
        <spring-security-crypto.version>6.4.5</spring-security-crypto.version>
        <spring-security-web.version>6.2.8</spring-security-web.version>
        <logback-core.version>1.5.13</logback-core.version>
        <logback-classic.version>1.5.13</logback-classic.version>
        <kotlin-stdlib.version>2.1.0</kotlin-stdlib.version>
        <software.amazon.awssdk.version>2.29.29</software.amazon.awssdk.version>
        <snakeyaml.version>2.0</snakeyaml.version>
        <commons.collections.version>3.2.2</commons.collections.version>
        <commons.beanutils.version>1.9.4</commons.beanutils.version>
        <jackson.databind.version>2.14.0</jackson.databind.version>
        <jetty.xml.version>10.0.16</jetty.xml.version>
        <org.hamcrest.version>2.2</org.hamcrest.version>
        <mockito.junit.jupiter.version>2.23.4</mockito.junit.jupiter.version>
        <org.powermock.version>1.7.0</org.powermock.version>
        <antlr4.version>4.13.2</antlr4.version>

        <freemaker.version>2.3.32</freemaker.version>

    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.boomi</groupId>
                <artifactId>boomi-dependencies-pom</artifactId>
                <version>${boomi-dependencies-pom.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>junit</groupId>
                        <artifactId>junit</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.powermock</groupId>
                        <artifactId>powermock-api-mockito</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.powermock</groupId>
                        <artifactId>powermock-module-junit4</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>${software.amazon.awssdk.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons.collections.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons.beanutils.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.databind.version}</version>
            </dependency>

            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-xml</artifactId>
                <version>${jetty.xml.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-http2</artifactId>
                <version>${netty.version}</version>
            </dependency>

            <dependency>
                <groupId>com.boomi</groupId>
                <artifactId>boomi-api-common</artifactId>
                <version>${boomi-api-common.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.boomi.services</groupId>
            <artifactId>service-liquibase</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.boomi.services</groupId>
            <artifactId>service-common-jobs</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
            <version>${liquibase.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>bedrockagent</artifactId>
            <version>${software.amazon.awssdk.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-handler</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sqs</artifactId>
            <version>${software.amazon.awssdk.version}</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>bedrock</artifactId>
            <version>${software.amazon.awssdk.version}</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>ssm</artifactId>
            <version>${software.amazon.awssdk.version}</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>organizations</artifactId>
            <version>${software.amazon.awssdk.version}</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>timestreamquery</artifactId>
            <version>${software.amazon.awssdk.version}</version>
            <exclusions>
                <!-- excluded for SNYK-JAVA-IONETTY-5953332 -->
                <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-codec-http2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>oam</artifactId>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>ec2</artifactId>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
        </dependency>
        <!-- Apache HTTP Client (recommended for better performance) -->
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>apache-client</artifactId>
        </dependency>
        <dependency>
                <groupId>com.boomi.metering</groupId>
                <artifactId>timestream-querier</artifactId>
                <version>1.0.4</version>
        </dependency>
        <dependency>
            <groupId>org.antlr</groupId>
            <artifactId>antlr4-runtime</artifactId>
            <version>${antlr4.version}</version>
        </dependency>
        <dependency>
            <groupId>com.boomi.services</groupId>
            <artifactId>service-common-webflux-graphql</artifactId>
            <version>${project.parent.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.protobuf</groupId>
                    <artifactId>protobuf-java</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                    <artifactId>spring-security-config</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                    <artifactId>spring-security-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                    <artifactId>spring-security-crypto</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                    <artifactId>spring-security-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jetbrains.kotlin</groupId>
                    <artifactId>kotlin-stdlib</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.boomi.bus</groupId>
            <artifactId>boomi-bus-transacted</artifactId>
            <version>1.46.2</version>
            <exclusions>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
            </exclusions>
            </dependency>
        <dependency>
            <groupId>com.boomi.redsky.model</groupId>
            <artifactId>model-graphql-server-ai-agent-registry</artifactId>
            <version>${graphqlmodel.version}</version>
        </dependency>
        <dependency>
            <groupId>io.hypersistence</groupId>
            <artifactId>hypersistence-utils-hibernate-63</artifactId>
            <version>${io.hypersistence.hibernate.version}</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sts</artifactId>
            <version>${software.amazon.awssdk.version}</version>
        </dependency>
        <dependency>
            <groupId>net.minidev</groupId>
            <artifactId>json-smart</artifactId>
            <version>${json.smart.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>${json.path.version}</version>
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <groupId>net.minidev</groupId>
                    <artifactId>json-smart</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.boomi</groupId>
            <artifactId>boomi-api-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.boomi.util</groupId>
            <artifactId>boomi-util</artifactId>
            <version>${boomi.util.version}</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>secretsmanager</artifactId>
            <version>${software.amazon.awssdk.version}</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback.contrib</groupId>
            <artifactId>logback-jackson</artifactId>
            <version>${logback.contrib.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback.contrib</groupId>
            <artifactId>logback-json-classic</artifactId>
            <version>${logback.contrib.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest</artifactId>
            <version>${org.hamcrest.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgres.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hamcrest</groupId>
                    <artifactId>hamcrest-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hamcrest</groupId>
                    <artifactId>hamcrest-library</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>${mockito.junit.jupiter.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>${org.powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>${testcontainers.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>software.aws.rds</groupId>
            <artifactId>aws-postgresql-jdbc</artifactId>
            <version>${aws-postgresql-jdbc.version}</version>
        </dependency>
        <dependency>
            <groupId>com.boomi.services</groupId>
            <artifactId>service-common-webflux-graphql-test</artifactId>
            <version>${project.parent.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>mockwebserver</artifactId>
            <version>${okhttp.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Version to fix SNYK-JAVA-IONETTY-8367012 -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-common</artifactId>
            <version>${netty-common.version}</version>
        </dependency>

        <!-- Version to fix SNYK-JAVA-IONETTY-8707739 -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-handler</artifactId>
            <version>${netty-handler.version}</version>
        </dependency>

        <!-- Version to fix SNYK-JAVA-COMGOOGLEPROTOBUF-8055227 -->
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>${protobuf-java.version}</version>
        </dependency>

        <!-- Version to fix SNYK-JAVA-COMGRAPHQLJAVA-7573314 -->
        <!-- Fix causes NoSuchMethod 'graphql.execution.ExecutionContext
        graphql.schema.DataFetchingEnvironment.getExecutionContext()' -->
        <!--
        <dependency>
            <groupId>com.graphql-java</groupId>
            <artifactId>graphql-java</artifactId>
            <version>21.5</version>
        </dependency>
        -->

        <!-- Version to fix SNYK-JAVA-ORGSPRINGFRAMEWORKSECURITY-8399272 -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-config</artifactId>
            <version>${spring-security-config.version}</version>
        </dependency>

        <!-- Version to fix SNYK-JAVA-ORGSPRINGFRAMEWORKSECURITY-8399269 -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
            <version>${spring-security-core.version}</version>
        </dependency>

        <!-- Version to fix SNYK-JAVA-ORGSPRINGFRAMEWORKSECURITY-8399273 -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
            <version>${spring-security-crypto.version}</version>
        </dependency>

        <!-- Version to fix SNYK-JAVA-ORGSPRINGFRAMEWORKSECURITY-8399278 -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-web</artifactId>
            <version>${spring-security-web.version}</version>
        </dependency>

        <!-- Version to fix SNYK-JAVA-CHQOSLOGBACK-8539865 and SNYK-JAVA-CHQOSLOGBACK-8539866 -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>${logback-core.version}</version>
        </dependency>

        <!-- Version to fix SNYK-JAVA-CHQOSLOGBACK-8539867 -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback-classic.version}</version>
        </dependency>

        <!-- Version to fix SNYK-JAVA-CHQOSLOGBACK-8539867 -->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>${kotlin-stdlib.version}</version>
        </dependency>
        <dependency>
            <groupId>com.newrelic.agent.java</groupId>
            <artifactId>newrelic-api</artifactId>
            <version>7.6.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>32.0.0-jre</version>
        </dependency>

        <!-- New Relic Agent -->
        <dependency>
            <groupId>com.newrelic.agent.java</groupId>
            <artifactId>newrelic-agent</artifactId>
            <version>7.6.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

    </dependencies>

    <build>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.kohsuke.args4j</groupId>
                    <artifactId>args4j-maven-plugin</artifactId>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0-M2</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok.mapstruct.binding.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <compilerArgs>  <!-- Add these args -->
                        <arg>-Amapstruct.defaultComponentModel=spring</arg>
                        <arg>-Amapstruct.verbose=true</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>unpack-graphql-schema</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>com.boomi.redsky.model</groupId>
                                    <artifactId>model-graphql-server-ai-agent-registry</artifactId>
                                    <outputDirectory>${project.build.directory}</outputDirectory>
                                    <includes>**\/*.graphql</includes>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-schema</id>
                        <phase>package</phase>
                        <goals>
                            <goal>attach-artifact</goal>
                        </goals>
                        <configuration>
                            <artifacts>
                                <artifact>
                                    <file>target/schema.graphql</file>
                                    <type>graphql</type>
                                    <classifier>schema</classifier>
                                </artifact>
                            </artifacts>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${surefire.failsafe.plugin.version}</version>
                <configuration>
                    <!-- Parent config blows up surefire.  This overrides the parent argLine config.  It can only be
                    overridden in this way; it cannot be empty or all spaces. -->
                    <argLine>${jacocoAgent} -Djava.security.egd=${unitTestPRNG} -Dx
                        -Dio.netty.tryReflectionSetAccessible=true --add-opens java.base/jdk.internal.misc=ALL-UNNAMED
                    </argLine>
                    <forkCount>1</forkCount>
                    <skipTests>${skipUTs}</skipTests>
                </configuration>
            </plugin>

            <plugin>
                <!-- This section only provides common configuration.  See profiles elsewhere in this pom for the
                     failsafe executions that have executions that run. -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <!-- plugin management version does NOT override version in plugin inherited from parent -->
                <version>${surefire.failsafe.plugin.version}</version>
                <configuration>
                    <skip>${skipIT}</skip>
                    <!-- The tests cannot find the production Configuration classes without this -->
                    <classesDirectory>${project.build.outputDirectory}</classesDirectory>
                    <!-- Parent config blows up failsafe.  This overrides the parent argLine config.  It can only be
                         overridden in this way; it cannot be empty or all spaces. -->
                    <argLine>${jacocoAgent}</argLine>
                    <redirectTestOutputToFile>true</redirectTestOutputToFile>
                    <workingDirectory>${project.build.directory}</workingDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <failOnError>false</failOnError>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.liquibase</groupId>
                <artifactId>liquibase-maven-plugin</artifactId>
                <version>${org.liquibase.maven.plugin.version}</version>
                <configuration>
                    <propertyFile>src/main/resources/liquibase.properties</propertyFile>
                    <propertyFileWillOverride>true</propertyFileWillOverride>
                    <changeLogFile>src/main/resources/db.changelog/changelog-master.xml</changeLogFile>
                    <logging>debug</logging>
                </configuration>
            </plugin>

        </plugins>
    </build>
    <scm>
        <connection>scm:git:*****************:boomii/ai-agent-registry-service</connection>
        <developerConnection>scm:git:*****************:boomii/ai-agent-registry-service</developerConnection>
        <tag>main</tag>
    </scm>
</project>
