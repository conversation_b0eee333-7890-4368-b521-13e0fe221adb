
package com.boomi.aiagentregistry.resolver.it.query;

import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentLlm;
import com.boomi.aiagentregistry.entity.AiAgentLlmAssociation;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTag;
import com.boomi.aiagentregistry.entity.AiAgentTagAssociation;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.repo.AiAgentListingRepository;
import com.boomi.aiagentregistry.util.AiAgentTestBuilder;
import com.boomi.aiagentregistry.util.CommonFeatureAndPrivilegeUtil;
import com.boomi.aiagentregistry.util.GraphQLFileReaderUtil;
import com.boomi.aiagentregistry.util.GraphQLQueriesEnum;
import com.boomi.aiagentregistry.util.GraphqlTypeEnum;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentsListingQueryInput;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import com.boomi.services.test.graphql.TestWebFluxGraphQLExecutor;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.JsonNode;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;

import static com.boomi.aiagentregistry.util.TestUtil.COMMON_EXTERNAL_ID;
import static com.boomi.aiagentregistry.util.TestUtil.PROVIDER_ACCOUNT_CREDENTIALS;
import static com.boomi.aiagentregistry.util.TestUtil.PROVIDER_ACCOUNT_METADATA;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class AiAgentListingServiceImplIntegrationTest extends BaseMockWebServerTest {

    @Autowired
    private AiAgentListingRepository aiAgentListingRepository;

    @Autowired
    private TestWebFluxGraphQLExecutor executor;

    @Autowired
    private TestUtil testUtil;
    AiAgentTag aiAgentTag = null;
    String providerAccountGuid;
    AiAgentProviderAccount aiAgentProviderAccount;

    @BeforeEach
    void setUp() {
        testUtil.cleanupDatabases();
        // create provider account
        aiAgentProviderAccount = createAccount();
        providerAccountGuid = aiAgentProviderAccount.getGuid();
        AiAgentsListingQueryInput input = new AiAgentsListingQueryInput();
        input.setOffset(0);
        input.setLimit(10);
        AiAgentLlm aiAgentLlm = testUtil.saveAiAgentLlm(aiAgentProviderAccount);
        AiAgentLlmAssociation aiAgentLlmAssociation = null;

        AiAgent aiAgent = testUtil.createAiAgent(aiAgentProviderAccount, GuidUtil.createAIAgentGuid(),
                COMMON_EXTERNAL_ID,
                false);

        AiAgentVersion aiAgentVersionOne = testUtil.createAiAgentVersion(aiAgent, GuidUtil.createAIAgentVersionGuid(),
                "test-llm-filter-ai-agent-version", "11", "RUNNING", "Instructions",
                false, null);

        // create Agent Tag
        AiAgentTagAssociation aiAgentTagAssociation = null;
        if (aiAgentTag == null && aiAgentVersionOne != null) {
            AiAgentTestBuilder.TagParams params = AiAgentTestBuilder.TagParams.builder()
                    .guid(GuidUtil.createAIAgentTagGuid())
                    .providerAccount(aiAgentProviderAccount)
                    .idpAccountId(aiAgentProviderAccount.getIdpAccountId())
                    .key("test-key")
                    .value("test-value")
                    .build();
            aiAgentTag = testUtil.createAiAgentTag(params);
        }

        // create Agent Tag Association
        if (aiAgentTagAssociation == null && aiAgentVersionOne != null && aiAgentTag != null) {
            AiAgentTestBuilder.TagAssociationParams params = AiAgentTestBuilder.TagAssociationParams.builder()
                    .guid(GuidUtil.createAIAgentTagAssociationGuid())
                    .tagUid(aiAgentTag.getUid())
                    .relatedEntityUid(aiAgentVersionOne.getUid())
                    .relatedEntityType(AiRegistryEntityType.VERSION)
                    .build();
            aiAgentTagAssociation = testUtil.createAiAgentTagAssociation(params);
        }

        // create LLM associations with aiAgentVersion
        if (aiAgentLlm == null && aiAgentVersionOne != null) {
            aiAgentLlm = testUtil.saveAiAgentLlm(aiAgentProviderAccount);
        }
        if (aiAgentLlmAssociation == null && aiAgentLlm != null) {
            aiAgentLlmAssociation = testUtil.saveAiAgentLlmAssociation(aiAgentLlm,
                    aiAgentVersionOne.getUid(),
                    AiRegistryEntityType.VERSION);
        }
        testUtil.createListings(aiAgentVersionOne, providerAccountGuid);
    }

    public AiAgentProviderAccount createAccount() {
        return testUtil.saveAiAgentRegistryAccount("boomi-test-b", TestUtil.COMMON_ACCOUNT_ID,
                PROVIDER_ACCOUNT_METADATA, PROVIDER_ACCOUNT_CREDENTIALS, AiAgentProviderType.AWS_BEDROCK,
                AiProviderAuthSchema.AWS, AiAgentProviderAccountStatus.CONNECTED);
    }

    @AfterEach
    void tearDown() {
        aiAgentListingRepository.deleteAll();
        testUtil.cleanupDatabases();
    }

    @Test
    @DisplayName("Test Successful execution of AiAgentListingQuery to fetch Agents with LLM filter")
    void testGetAiAgentListings_WithLlmFilter() throws IOException {

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_LISTING_FILTER.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("     accounts: [\"accGuid\"]", " models: [\"agent-llm-name\"]");
        JsonNode aiAgentsData = getListingData(queryStr);
        assertEquals(1, aiAgentsData.get("numberOfResults").asInt());
    }

    @Test
    @DisplayName("Test Successful execution of AiAgentListingQuery to fetch Agents")
    public void testBasicQuery() throws IOException {
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_LISTING.getFileName());
        assertNotNull(queryStr);
        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        JsonNode aiAgentsData = rootNode.get("data").get("aiAgentListings");
        assertNotNull(aiAgentsData);
        assertEquals(3, aiAgentsData.get("numberOfResults").asInt());
    }

    @Test
    @DisplayName("Test Successful execution of AiAgentListingQuery to fetch Agents with searchText as input")
    void testGetAiAgentListings_WithSearchText() throws IOException {
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_LISTING_SEARCH.getFileName());
        assertNotNull(queryStr);
        JsonNode aiAgentsData = getListingData(queryStr);
        assertEquals(1, aiAgentsData.get("numberOfResults").asInt());
    }

    @Test
    @DisplayName("Test Successful execution of AiAgentListingQuery to fetch Agents with matching account filter input")
    void testGetAiAgentListings_WithAccountFilter() throws IOException {
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_LISTING_FILTER.getFileName());

        assertNotNull(queryStr);
        queryStr = queryStr.replace("accGuid", providerAccountGuid);

        JsonNode aiAgentsData = getListingData(queryStr);
        assertNotNull(aiAgentsData);
        assertEquals(3, aiAgentsData.get("numberOfResults").asInt());
        // Verify provider account GUID
        assertEquals(providerAccountGuid,
                aiAgentsData.get("aiAgentListings").get(0).get("providerAccountGuId").asText(),
                "Provider account GUID should match expected value");
    }

    @Test
    void testGetAiAgentListings_WithAccountFilterWithDefaultCondition() throws IOException {
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_LISTING_FILTER.getFileName());

        assertNotNull(queryStr);
        queryStr = queryStr.replace("accGuid", providerAccountGuid);
        queryStr = queryStr.replace("condition: OR", "");

        JsonNode aiAgentsData = getListingData(queryStr);
        assertEquals(3, aiAgentsData.get("numberOfResults").asInt());

        // Get the first listing
        JsonNode responseAgent = aiAgentsData.get("aiAgentListings").get(0);
        assertNotNull(responseAgent);
        // Verify provider account GUID
        assertEquals(providerAccountGuid,
                responseAgent.get("providerAccountGuId").asText(),
                "Provider account GUID should match expected value");
    }

    @Test
    @DisplayName("Test Successful execution of AiAgentListingQuery to fetch Agents with matching providerType filter "
            + "input")
    void testGetAiAgentListings_WithProviderTypeFilter() throws IOException {

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_LISTING_FILTER.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("     accounts: [\"accGuid\"]", " providers: [AWS_BEDROCK]");
        JsonNode aiAgentsData = getListingData(queryStr);
        assertEquals(2, aiAgentsData.get("numberOfResults").asInt());
    }

    @Test
    @DisplayName("Test Successful execution of AiAgentListingQuery to fetch Agents with matching tag filter input")
    void testGetAiAgentListings_WithProviderTypeFilterTag() throws IOException {

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_LISTING_FILTER_TAG.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("tagGuid", aiAgentTag.getGuid());
        JsonNode aiAgentsData = getListingData(queryStr);
        assertEquals(1, aiAgentsData.get("numberOfResults").asInt());
    }

    @Test
    @DisplayName("Test Successful execution of AiAgentListingQuery to fetch Agents with matching account filter  "
            + "and default limit and offset ")
    void testGetAiAgentListings_WithProviderTypeFilter_DefaultLimit_AND_Offset() throws IOException {

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_LISTING_FILTER.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("     accounts: [\"accGuid\"]", " providers: [AWS_BEDROCK]");
        queryStr = queryStr.replace("offset: 0\n"
                + "        limit: 10", "");
        JsonNode aiAgentsData = getListingData(queryStr);
        assertEquals(2, aiAgentsData.get("numberOfResults").asInt());
    }

    JsonNode getListingData(String queryStr) throws IOException {
        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        JsonNode aiAgentsData = rootNode.get("data").get("aiAgentListings");
        assertNotNull(aiAgentsData);
        return aiAgentsData;
    }

    @Test
    @DisplayName("Test  AiAgentListingQuery with invalid input and return corresponding errors in response")
    void testGetAiAgentListings_WithProviderTypeFilter_InvalidInput() throws IOException {

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_LISTING_FILTER.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("     accounts: [\"accGuid\"]", " providers: [AWS_BEDROCK]");
        queryStr = queryStr.replace("offset: 0\n"
                + "        limit: 10", "offset: -50\n"
                + "        limit: -70");
        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        assertTrue(rootNode.has("errors"));
    }

    @Test
    @DisplayName("Test  AiAgentListingQuery with invalid limit input and return corresponding errors in response")
    void testGetAiAgentListings_WithInvalidLimit() throws IOException {

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_LISTING_FILTER_TAG.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("10", "-10");
        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        assertTrue(rootNode.has("errors"));
    }

    @Test
    @DisplayName("Test  AiAgentListingQuery with invalid search input and return corresponding errors in response")
    void testGetAiAgentListings_WithInvalidSearchText() throws IOException {
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_LISTING_SEARCH.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("arithmetic", " ");
        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        assertTrue(rootNode.has("errors"));
    }

    @Test
    @DisplayName("Should successfully execute query and return no records")
    void testGetAiAgentListings_WithSearch_No_Matching() throws IOException {
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_LISTING_SEARCH.getFileName());
        assertNotNull(queryStr);
        queryStr = queryStr.replace("arithmetic", "abc");
        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(response);
        assertEquals("{\"data\":{\"aiAgentListings\":null}}", response);
    }
}


