// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.auth;

import software.amazon.awssdk.services.sts.model.AssumeRoleResponse;
import com.boomi.aiagentregistry.service.BedrockAssumeRoleService;
import com.boomi.aiagentregistry.servlet.AwsClient;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AwsAssumeRoleAuthorizationParserStrategyTest {
    @Mock
    private ObjectMapper _objectMapper;

    @Mock
    private AwsClient _awsClient;

    @Mock
    private BedrockAssumeRoleService _bedrockAssumeRoleService;

    private AwsAssumeRoleAuthorizationParserStrategy _authorizationParserStrategy;

    @BeforeEach
    void setUp() {
        _authorizationParserStrategy = new AwsAssumeRoleAuthorizationParserStrategy(
                _objectMapper, _awsClient, _bedrockAssumeRoleService);
    }

    @Test
    void refreshTempAccessTokenIfExpired_WhenTokenIsAboutToExpire_ShouldRefreshToken() {
        // Arrange
        String awsAccountId = "test-assume-role-provider-account";
        String awsRegion = "us-east-1";
        String externalId = "test-external-id";
        int refreshWindowInMinutes = 20;
        long currentTime = System.currentTimeMillis();
        long expirationTime = currentTime + TimeUnit.MINUTES.toMillis(19);

        AwsCredentials existingCredentials = new AwsCredentials();
        existingCredentials.setExpirationEpochMilli(expirationTime);
        existingCredentials.setAwsAccountId(awsAccountId);
        existingCredentials.setAwsRegion(awsRegion);
        existingCredentials.setAwsAccessKeyId("test-aws-access-key-id");
        existingCredentials.setAwsSecretAccessKey("test-aws-secret-access-key-id");
        existingCredentials.setSessionToken("test-session-token");
        existingCredentials.setExternalId(externalId);

        long newExpirationTime = currentTime + TimeUnit.HOURS.toMillis(1);

        // mock AssumeRoleResponse
        software.amazon.awssdk.services.sts.model.Credentials assumeRoleCredentials =
                software.amazon.awssdk.services.sts.model.Credentials.builder()
                        .accessKeyId("test-aws-access-key-id")
                        .secretAccessKey("test-aws-secret-access-key-id")
                        .sessionToken("test-new-session-token")
                        .expiration(Instant.ofEpochMilli(newExpirationTime))
                        .build();
        AssumeRoleResponse assumeRoleResponse =
                AssumeRoleResponse.builder()
                                  .credentials(software.amazon.awssdk.services.sts.model.Credentials.builder()
                                      .accessKeyId("test-aws-access-key-id")
                                      .secretAccessKey("test-aws-secret-access-key-id")
                                      .sessionToken("test-new-session-token")
                                      .expiration(Instant.ofEpochMilli(newExpirationTime))
                                      .build())
                                  .build();

        when(_bedrockAssumeRoleService.getAssumeRoleResponse(
                eq(awsAccountId),
                eq(awsRegion),
                eq(externalId)
        )).thenReturn(assumeRoleResponse);

        // Act
        Credentials refreshedCredentials = _authorizationParserStrategy
                .refreshTempAccessTokenIfExpired(existingCredentials, refreshWindowInMinutes);

        // Assert
        assertNotNull(refreshedCredentials);
        assertInstanceOf(AwsCredentials.class, refreshedCredentials);
        assertEquals(newExpirationTime,
                ((AwsCredentials)refreshedCredentials).getExpirationEpochMilli());
        verify(_bedrockAssumeRoleService).getAssumeRoleResponse(existingCredentials.getAwsAccountId(),
                existingCredentials.getAwsRegion(), existingCredentials.getExternalId());
    }

    @Test
    void refreshTempAccessTokenIfExpired_WhenTokenIsNotExpiring_ShouldReturnNull() {
        // Arrange
        String awsAccountId = "test-assume-role-provider-account";
        String awsRegion = "us-east-1";
        String externalId = "test-external-id";
        int refreshWindowInMinutes = 20;
        long currentTime = System.currentTimeMillis();
        long expirationTime = currentTime + TimeUnit.MINUTES.toMillis(21);

        AwsCredentials existingCredentials = new AwsCredentials();
        existingCredentials.setExpirationEpochMilli(expirationTime);
        existingCredentials.setAwsAccountId(awsAccountId);
        existingCredentials.setAwsRegion(awsRegion);
        existingCredentials.setAwsAccessKeyId("test-aws-access-key-id");
        existingCredentials.setAwsSecretAccessKey("test-aws-secret-access-key-id");
        existingCredentials.setSessionToken("test-session-token");
        existingCredentials.setExternalId(externalId);

        // Act
        Credentials refreshedCredentials = _authorizationParserStrategy
                .refreshTempAccessTokenIfExpired(existingCredentials, refreshWindowInMinutes);

        // Assert
        assertNull(refreshedCredentials);
    }

    @Test
    void refreshTempAccessTokenIfExpired_WhenTokenIsAlreadyExpired_ShouldRefreshToken() {
        // Arrange
        String awsAccountId = "test-assume-role-provider-account";
        String awsRegion = "us-east-1";
        String externalId = "test-external-id";
        int refreshWindowInMinutes = 20;
        long currentTime = System.currentTimeMillis();
        long expirationTime = currentTime - TimeUnit.MINUTES.toMillis(5);

        AwsCredentials existingCredentials = new AwsCredentials();
        existingCredentials.setExpirationEpochMilli(expirationTime);
        existingCredentials.setAwsAccountId(awsAccountId);
        existingCredentials.setAwsRegion(awsRegion);
        existingCredentials.setAwsAccessKeyId("test-aws-access-key-id");
        existingCredentials.setAwsSecretAccessKey("test-aws-secret-access-key-id");
        existingCredentials.setSessionToken("test-session-token");
        existingCredentials.setExternalId(externalId);

        long newExpirationTime = currentTime + TimeUnit.HOURS.toMillis(1);

        // mock AssumeRoleResponse
        software.amazon.awssdk.services.sts.model.Credentials assumeRoleCredentials =
                software.amazon.awssdk.services.sts.model.Credentials.builder()
                                         .accessKeyId("test-aws-access-key-id")
                                         .secretAccessKey("test-aws-secret-access-key-id")
                                         .sessionToken("test-new-session-token")
                                         .expiration(Instant.ofEpochMilli(newExpirationTime))
                                         .build();
        AssumeRoleResponse assumeRoleResponse =
                AssumeRoleResponse.builder()
                                  .credentials(software.amazon.awssdk.services.sts.model.Credentials.builder()
                                        .accessKeyId("test-aws-access-key-id")
                                        .secretAccessKey("test-aws-secret-access-key-id")
                                        .sessionToken("test-new-session-token")
                                        .expiration(Instant.ofEpochMilli(newExpirationTime))
                                        .build())
                                  .build();

        when(_bedrockAssumeRoleService.getAssumeRoleResponse(
                eq(awsAccountId),
                eq(awsRegion),
                eq(externalId)
        )).thenReturn(assumeRoleResponse);

        // Act
        Credentials refreshedCredentials = _authorizationParserStrategy
                .refreshTempAccessTokenIfExpired(existingCredentials, refreshWindowInMinutes);

        // Assert
        assertNotNull(refreshedCredentials);
        assertInstanceOf(AwsCredentials.class, refreshedCredentials);
        assertEquals(newExpirationTime,
                ((AwsCredentials)refreshedCredentials).getExpirationEpochMilli());
        verify(_bedrockAssumeRoleService).getAssumeRoleResponse(existingCredentials.getAwsAccountId(),
                existingCredentials.getAwsRegion(), existingCredentials.getExternalId());
    }

}
