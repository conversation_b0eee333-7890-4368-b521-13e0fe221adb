// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.util;

import lombok.Getter;

@Getter
public enum GraphQLQueriesEnum {
    AI_AGENT_GRAPH_METRICS("GetAiAgentGraphMetrics.graphql"),
    AI_AGENT_AGGREGATE_METRICS("GetAiAgentAggregateMetrics.graphql"),
    AI_AGENT_INVOCATIONS_METRICS("GetAiAgentInvocationsMetrics.graphql"),
    AI_AGENT_PROVIDER_ACCOUNT_FOR_BOOMI_SUCCESS("AiAgentProviderAccountForBoomi.graphql"),
    AI_AGENT_PROVIDER_ACCOUNT_FOR_CUSTOM_SUCCESS("AiAgentProviderAccountForCustom.graphql"),
    AI_AGENT_PROVIDER_ACCOUNT_FOR_BEDROCK_ASSUME_ROLE_SUCCESS("AiAgentProviderAccountForAwsAssumeRole.graphql"),
    AI_AGENT_PROVIDER_ACCOUNT_STEP2_FOR_BEDROCK("AiAgentProviderStep2ForAws.graphql"),
    AI_AGENT_PROVIDER_ACCOUNT_FOR_INVALID_CREDENTIALS(
            "AiAgentProviderAccountForInvalidCredentials.graphql"),
    TEST_AI_AGENT_TOOL_CREATE("TestAiAgentToolCreate.graphql"),
    TEST_AI_AGENT_TOOL_UPDATE("TestAiAgentToolUpdate.graphql"),
    AI_AGENT_PROVIDER_ACCOUNTS_GET("GetAIAgentProviderAccounts.graphql"),
    AI_AGENT_PROVIDER_ACCOUNTS_FILTER_GET("GetAIAgentProviderAccountsFilter.graphql"),
    AI_AGENT_PROVIDER_PROVIDERS("GetAiAgentProviders.graphql"),
    AI_AGENT_PROVIDER_ACCOUNT_UPDATE_INPUT_VALIDATION("AiAgentProviderAccountUpdateInputValidation.graphql"),
    AI_AGENT_PROVIDER_ACCOUNT_UPDATE("AiAgentProviderAccountUpdate.graphql"),
    AI_AGENT_PROVIDER_ACCOUNT("AiAgentProviderAccount.graphql"),
    AI_AGENT_CREATE("AiAgentCreate.graphql"),
    AI_AGENT_UPDATE("AiAgentUpdate.graphql"),
    AI_AGENTS("AiAgents.graphql"),
    AI_AGENT_TAG_QUERY("AiAgentTags.graphql"),
    AI_AGENT_TAG_SYNC_MUTATION("AiAgentTagAddMutation.graphql"),
    AI_AGENT_BY_ALIAS_ID("AiAgentByAliasId.graphql"),
    AI_AGENT_BY_VERSION_ID("AiAgentByVersionId.graphql"),
    AI_AGENT_VERSION_TRUST_LEVEL("AiAgentVersionTrustLevelAdd.graphql"),
    AI_AGENT_REGIONS("AiAgentRegions.graphql"),
    AI_AGENT_VERSION_ENABLE_MUTATION("AiAgentVersionEnable.graphql"),
    AI_AGENT_PROVIDER_ACCOUNT_DELETE("AiAgentProviderAccountDelete.graphql"),
    AI_AGENT_TRUST_LEVEL("AiAgentTrustLevel.graphql"),
    AI_AGENT_LLM("AiAgentLlm.graphql"),
    AI_AGENT_PROVIDER_ACCOUNT_SYNC("AiAgentProviderAccountSync.graphql"),
    AI_AGENT_LISTING("AiAgentListings.graphql"),
    AI_AGENT_LISTING_SEARCH("AiAgentListingsWithSearch.graphql"),
    AI_AGENT_LISTING_FILTER_TAG("AiAgentListingsWithFilterTag.graphql"),
    AI_AGENT_LISTING_FILTER("AiAgentListingsWithFilter.graphql");
    private final String fileName;
    GraphQLQueriesEnum(String fileName) {
        this.fileName = fileName;
    }
}
