// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.util;

import com.boomi.aiagentregistry.entity.AgentEntitySyncLatest;
import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentAlias;
import com.boomi.aiagentregistry.entity.AiAgentGuardrail;
import com.boomi.aiagentregistry.entity.AiAgentGuardrailAssociation;
import com.boomi.aiagentregistry.entity.AiAgentLargeTextContent;
import com.boomi.aiagentregistry.entity.AiAgentListing;
import com.boomi.aiagentregistry.entity.AiAgentLlm;
import com.boomi.aiagentregistry.entity.AiAgentLlmAssociation;
import com.boomi.aiagentregistry.entity.AiAgentProviderAccount;
import com.boomi.aiagentregistry.entity.AiAgentTag;
import com.boomi.aiagentregistry.entity.AiAgentTagAssociation;
import com.boomi.aiagentregistry.entity.AiAgentTask;
import com.boomi.aiagentregistry.entity.AiAgentTaskAssociation;
import com.boomi.aiagentregistry.entity.AiAgentTool;
import com.boomi.aiagentregistry.entity.AiAgentToolAssociation;
import com.boomi.aiagentregistry.entity.AiAgentToolResource;
import com.boomi.aiagentregistry.entity.AiAgentVersion;
import com.boomi.aiagentregistry.entity.Auditable;
import com.boomi.aiagentregistry.entity.VersionAliasIdKey;
import com.boomi.aiagentregistry.repo.AiAgentAliasRepository;
import com.boomi.aiagentregistry.repo.AiAgentGuardrailAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentGuardrailRepository;
import com.boomi.aiagentregistry.repo.AiAgentLargeTextContentRepository;
import com.boomi.aiagentregistry.repo.AiAgentLatestSyncRepository;
import com.boomi.aiagentregistry.repo.AiAgentListingRepository;
import com.boomi.aiagentregistry.repo.AiAgentLlmAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentLlmRepository;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;
import com.boomi.aiagentregistry.repo.AiAgentProviderSyncRepository;
import com.boomi.aiagentregistry.repo.AiAgentRepository;
import com.boomi.aiagentregistry.repo.AiAgentTagAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentTagRepository;
import com.boomi.aiagentregistry.repo.AiAgentTaskAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentTaskRepository;
import com.boomi.aiagentregistry.repo.AiAgentToolAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentToolRepository;
import com.boomi.aiagentregistry.repo.AiAgentToolResourceRepository;
import com.boomi.aiagentregistry.repo.AiAgentVersionRepository;
import com.boomi.aiagentregistry.repo.AuditLogRepository;
import com.boomi.aiagentregistry.repo.SyncUserAuditRepository;
import com.boomi.aiagentregistry.service.SecretsManagerService;
import com.boomi.graphql.server.schema.types.AiAgentProviderAccountStatus;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.boomi.graphql.server.schema.types.AiRegistryEntitySyncStatus;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.sql.Timestamp;
import java.time.Clock;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.UUID;
import java.util.stream.IntStream;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.DELIMITER;
import static org.mockito.Mockito.mock;

@Service
public class TestUtil {

    @Autowired
    private SecretsManagerService _secretsManagerService;

    @Autowired
    private AiAgentProviderAccountRepository _aiAgentProviderAccountRepository;

    @Autowired
    private AiAgentProviderSyncRepository _aiAgentProviderSyncRepository;

    @Autowired
    private AiAgentLatestSyncRepository _aiAgentLatestSyncRepository;

    @Autowired
    private AiAgentToolRepository _aiAgentToolRepository;

    @Autowired
    private AiAgentToolResourceRepository _resourceRepository;

    @Autowired
    AuditLogRepository _auditLogRepository;

    @Autowired
    AiAgentRepository _aiAgentRepository;

    @Autowired
    AiAgentGuardrailRepository _aiAgentGuardrailRepository;

    @Autowired
    AiAgentVersionRepository _aiAgentVersionRepository;

    @Autowired
    AiAgentAliasRepository _aiAgentAliasRepository;

    @Autowired
    AiAgentGuardrailAssociationRepository _aiAgentGuardrailAssociationRepository;

    @Autowired
    AiAgentToolAssociationRepository _agentToolAssociationRepository;

    @Autowired
    AiAgentTagRepository _aiAgentTagRepository;

    @Autowired
    AiAgentTagAssociationRepository _aiAgentTagAssociationRepository;

    @Autowired
    AiAgentLlmRepository _aiAgentLlmRepository;

    @Autowired
    AiAgentLlmAssociationRepository _aiAgentLlmAssociationRepository;

    @Autowired
    AiAgentLargeTextContentRepository _largeTextRepo;

    @Autowired
    SyncUserAuditRepository _syncUserAuditRepository;
    @Autowired
    AiAgentTaskAssociationRepository _agentTaskAssociationRepository;
    @Autowired
    private AiAgentTaskRepository _aiAgentTaskRepository;

    @Autowired
    private AiAgentListingRepository _aiAgentListingRepository;

    public static final String COMMON_ACCOUNT_ID = "boomi-internal";
    public static final String COMMON_USER_NAME = "<EMAIL>";
    public static final String ACCOUNT_ID = "test-account-id";
    public static final String TEST_REGISTRY_ACCOUNT = "test_registry_account";
    public static final String US_EAST_1 = "us-east-1";
    public static final String AI_AGENT_EXTERNAL_ID_1 = "aiAgentExternalId1";
    public static final String AI_AGENT_EXTERNAL_ID_2 = "aiAgentExternalId2";
    public static final String AI_AGENT_EXTERNAL_ID_3 = "aiAgentExternalId3";
    public static final String COMMON_EXTERNAL_ID = "test-external-id";
    public static final Timestamp CREATED_DATE = Timestamp.valueOf("2024-09-10 09:01:15.0");
    public static final Timestamp MODIFIED_DATE = Timestamp.valueOf("2024-09-10 09:01:15.0");
    public static final String PROVIDER_ACCOUNT_METADATA =
            """
                    {
                      "awsAccessKeyId": "Test Value",
                      "awsSecretAccessKey": "Test Key",
                      "awsRegion": \
                    "ab-east-1"
                    }""";
    public static final String PROVIDER_ACCOUNT_CREDENTIALS = """
            {
              "userName": "<EMAIL>",
              "apiToken": "boomi",
              \
            "accountId":"boomi-internal"
            }""";

    // will have all the db operation

    public static <T> T parseGraphqlResponse(String payload, String operationName, Class<T> returnType)
            throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode node = mapper.readTree(payload);
        String operationResult = node.get("data").get(operationName).toString();
        return mapper.readValue(operationResult, returnType);
    }

    public static List<AiAgentProviderAccount> createTestProviders() {
        List<AiAgentProviderAccount> providers = new ArrayList<>();
        String[] regions = {US_EAST_1, "us-west-2", "eu-west-1"};
        AiAgentProviderType[] providerTypes =
                {AiAgentProviderType.AWS_BEDROCK, AiAgentProviderType.BOOMI, AiAgentProviderType.BOOMI,};
        AiProviderAuthSchema[] authSchemas =
                {AiProviderAuthSchema.BOOMIAPITOKEN, AiProviderAuthSchema.AWS, AiProviderAuthSchema.APITOKEN};
        AiAgentProviderAccountStatus[] providerStatuses = {
                AiAgentProviderAccountStatus.CONNECTED, AiAgentProviderAccountStatus.DISABLED,
                AiAgentProviderAccountStatus.SYNCING};
        Set<AiAgent> agents = new HashSet<>();
        AiAgent agent = mock(AiAgent.class);
        agent.setGuid(GuidUtil.createAIAgentToolGuid());
        agent.setIsDeleted(false);
        agent.setIdpAccountId("boomi-internal");
        AiAgentProviderAccount registryAccount = mock(AiAgentProviderAccount.class);
        registryAccount.setProviderAccountStatus(AiAgentProviderAccountStatus.CONNECTED);
        registryAccount.setProviderAccountName("Test Account");
        agent.setAiAgentProviderAccount(registryAccount);
        agents.add(agent);

        for (int i = 0; i < 3; i++) {
            AiAgentProviderAccount provider = new AiAgentProviderAccount();
            provider.setUid(i);
            provider.setGuid(GuidUtil.createAIAgentToolGuid());
            provider.setIdpAccountId(ACCOUNT_ID);
            provider.setProviderAccountStatus(providerStatuses[i]);
            provider.setProviderType(providerTypes[i]);
            provider.setAuthSchema(authSchemas[i]);
            provider.setRegion(regions[i]);
            provider.setProviderAccountName("test_registry_account");
            provider.setAgents(agents);
            providers.add(provider);
        }
        return providers;
    }

    public static String readResourceAsString(String fileName) throws IOException {
        Resource resource = new ClassPathResource(fileName);
        return Files.readString(Path.of(resource.getURI()), StandardCharsets.UTF_8);
    }

    public AiAgentTool saveAiAgentTool(String externalId, AiAgentProviderAccount registryAccount, String idpAccountId,
                                       String toolName,
                                       String versionString,
                                       Integer versionInt,
                                       String toolJson) {
        return saveAiAgentTool(externalId, registryAccount, idpAccountId, toolName, versionString, versionInt,
                toolJson, null);
    }

    public AiAgentTool saveAiAgentTool(String externalId, AiAgentProviderAccount registryAccount, String idpAccountId,
                                       String toolName,
                                       String versionString,
                                       Integer versionInt,
                                       String toolJson,
                                       Timestamp updatedAtProviderTime) {
        AiAgentTool aiAgentTool = new AiAgentTool();
        String guid = GuidUtil.createAIAgentToolGuid();
        aiAgentTool.setGuid(guid);
        aiAgentTool.setExternalId(externalId);
        aiAgentTool.setAiAgentProviderAccount(registryAccount);
        aiAgentTool.setIdpAccountId(idpAccountId);
        aiAgentTool.setName(toolName);
        aiAgentTool.setVersionString(versionString);
        aiAgentTool.setVersionInt(versionInt);
        aiAgentTool.setToolJson(toolJson);
        aiAgentTool.setUpdatedAtProviderTime(updatedAtProviderTime);
        aiAgentTool.setCreatedByUserId("<EMAIL>");
        aiAgentTool.setCreatedTime(AuditUtilImpl.getCurrentTime());
        aiAgentTool.setModifiedTime(AuditUtilImpl.getCurrentTime());
        return _aiAgentToolRepository.save(aiAgentTool);
    }

    public static List<AiAgent> createTestAiAgents() {
        List<AiAgent> aiAgents = new ArrayList<>();
        final AiAgentProviderAccount aiAgentProviderAccount = getAiAgentProviderAccount();

        AiAgent aiAgent = new AiAgent();
        aiAgent.setUid(1);
        aiAgent.setAiAgentProviderAccount(aiAgentProviderAccount);
        aiAgent.setExternalId("sampleExternalId");
        aiAgent.setIdpAccountId(ACCOUNT_ID);

        AiAgent aiAgent2 = new AiAgent();
        aiAgent2.setUid(2);
        aiAgent2.setAiAgentProviderAccount(aiAgentProviderAccount);
        aiAgent2.setExternalId("sampleExternalId2");
        aiAgent2.setIdpAccountId(ACCOUNT_ID);

        AiAgent aiAgent3 = new AiAgent();
        aiAgent3.setUid(3);
        aiAgent3.setAiAgentProviderAccount(aiAgentProviderAccount);
        aiAgent3.setExternalId("sampleExternalId3");
        aiAgent3.setIdpAccountId(ACCOUNT_ID);

        aiAgents.addAll(List.of(aiAgent, aiAgent2, aiAgent3));
        return aiAgents;
    }

    private static @NotNull AiAgentProviderAccount getAiAgentProviderAccount() {
        AiAgentProviderAccount aiAgentProviderAccount = new AiAgentProviderAccount();
        aiAgentProviderAccount.setIdpAccountId(ACCOUNT_ID);
        aiAgentProviderAccount.setExternalProviderAccountName(TEST_REGISTRY_ACCOUNT);
        aiAgentProviderAccount.setProviderAccountStatus(AiAgentProviderAccountStatus.CONNECTED);
        aiAgentProviderAccount.setProviderType(AiAgentProviderType.BOOMI);
        aiAgentProviderAccount.setAuthSchema(AiProviderAuthSchema.BOOMIAPITOKEN);
        aiAgentProviderAccount.setRegion(US_EAST_1);
        return aiAgentProviderAccount;
    }

    public List<AiAgentProviderAccount> createAiAgentProviders(int numberOfProviderAccountsToCreate) {
        return IntStream.range(0, numberOfProviderAccountsToCreate)
                .mapToObj(this::createAiAgentProvider)
                .toList();
    }

    public AiAgentProviderAccount createAiAgentProvider() {
        return createAiAgentProvider(1);
    }

    public AiAgentProviderAccount saveAiAgentRegistryAccount(String providerAccountName, String idpAccountId, String metaData,
                                                             String credentials, AiAgentProviderType aiAgentProviderType, AiProviderAuthSchema authSchema,
                                                             AiAgentProviderAccountStatus aiAgentProviderAccountStatus) {

        String guid = GuidUtil.createAIAgentProviderGuid();
        String secretName = COMMON_ACCOUNT_ID.concat(DELIMITER.concat(String.valueOf(guid)));
        _secretsManagerService.createSecret(secretName, credentials);

        AiAgentProviderAccount provider = new AiAgentProviderAccount();
        provider.setGuid(guid);
        provider.setIdpAccountId(idpAccountId);
        provider.setProviderAccountStatus(aiAgentProviderAccountStatus);
        provider.setProviderType(aiAgentProviderType);
        provider.setAuthSchema(authSchema);
        provider.setRegion("us-east-1");
        provider.setCredentialsKey(secretName);
        provider.setProviderMetadata(metaData);
        provider.setProviderAccountName(providerAccountName);
        provider.setExternalProviderAccountId("test-provider-account-id");
        provider.setExternalProviderAccountName("test-provider-account-name");
        setAuditFields(provider);

        return _aiAgentProviderAccountRepository.saveAndFlush(provider);
    }

    public AgentEntitySyncLatest saveAgentEntitySyncHistory(Integer entityUid, AiRegistryEntitySyncStatus syncStatus,
                                                            Timestamp syncStartDate, Timestamp syncEndDate,

                                                            AiRegistryEntityType entityType) {

        // Then create and save the latest sync
        AgentEntitySyncLatest syncLatest = new AgentEntitySyncLatest();
        syncLatest.setSyncedEntityUid(entityUid);
        syncLatest.setSyncStatus(syncStatus);
        syncLatest.setSyncStartDate(syncStartDate);
        syncLatest.setSyncEndDate(syncEndDate);
        syncLatest.setSyncedEntityType(entityType);

        return _aiAgentLatestSyncRepository.save(syncLatest);
    }

    public static String getLanguageTag() {
        Locale currentLocale = Locale.getDefault();
        return currentLocale.toLanguageTag();  // e.g., "en-US"
    }

    public AiAgentProviderAccount createBedrockTestAccount() {
        AiAgentProviderAccount account = AiAgentProviderAccount.builder()
                .uid(1)
                .idpAccountId("test-account")
                .guid("8214f9d4-398B-41fb-b454-00000000e3f6")
                .authSchema(AiProviderAuthSchema.AWS)
                .credentialsKey("omarqa-2U5C8E/8214f9d4-398B-41fb-b454-00000000e3f6")
                .providerType(AiAgentProviderType.AWS_BEDROCK)
                .isDeleted(false)
                .build();
        return _aiAgentProviderAccountRepository.save(account);
    }

    public AiAgentProviderAccount createGardenTestAccount() {
        AiAgentProviderAccount account = AiAgentProviderAccount.builder()
                .uid(1)
                .idpAccountId("test-account")
                .guid("8214f9d4-398B-41fb-b454-00000000e3f6")
                .authSchema(AiProviderAuthSchema.BOOMIAPITOKEN)
                .credentialsKey("pumane-2U5C8E/8214f9d4-398B-41fb-b454-00000000e3f6")
                .providerType(AiAgentProviderType.BOOMI)
                .isDeleted(false)
                .build();
        return _aiAgentProviderAccountRepository.save(account);
    }

    public AiAgent createAiAgent(AiAgentProviderAccount account, String uuid, String externalId, boolean isDeleted) {
        AiAgent aiAgent = AiAgent.builder()
                .idpAccountId(account.getIdpAccountId())
                .guid(uuid)
                .externalId(externalId)
                .aiAgentProviderAccount(account)
                .isDeleted(isDeleted)
                .build();
        return _aiAgentRepository.save(aiAgent);
    }

    public AiAgentVersion createAiAgentVersion(AiAgent aiAgent, String uuid, String name, String version,
                                               String agentStatus, String instructions, boolean isDeleted, Timestamp updatedAt) {
        AiAgentVersion aiAgentVersion = AiAgentVersion.builder()
                .guid(uuid)
                .name(name)
                .agent(aiAgent)
                .versionString(version)
                .agentStatus(agentStatus)
                .isDeleted(isDeleted)
                .externalId("version-external-id")
                .updatedAtProviderTime(updatedAt)
                .createdByUserId(COMMON_USER_NAME)
                .createdTime(CREATED_DATE)
                .modifiedByUserId(COMMON_USER_NAME)
                .modifiedTime(MODIFIED_DATE)
                .build();

        AiAgentVersion savedAiAgentVersion = _aiAgentVersionRepository.save(aiAgentVersion);

        Clock fixedClockStartTime = Clock.fixed(Instant.parse("2024-01-10T10:00:00Z"), ZoneId.systemDefault());
        Timestamp now = new Timestamp(fixedClockStartTime.millis());
        Clock fixedClockEndTime = Clock.fixed(Instant.parse("2024-01-10T10:10:00Z"), ZoneId.systemDefault());
        Timestamp later = new Timestamp(fixedClockEndTime.millis());

        saveAgentEntitySyncHistory(savedAiAgentVersion.getUid(), AiRegistryEntitySyncStatus.COMPLETED, now, later,
                AiRegistryEntityType.VERSION);
        return savedAiAgentVersion;
    }

    public AiAgentAlias createVersionAlias(AiAgentVersion version, String uuid, String externalId, boolean isDeleted,
                                           Timestamp updatedAt, String name, String description) {
        AiAgentAlias aiAgentAlias = AiAgentAlias.builder()
                .guid(uuid)
                .agentVersion(version)
                .externalId(externalId)
                .isDeleted(isDeleted)
                .updatedAtProviderTime(updatedAt)
                .name(name)
                .description(description)
                .build();
        return _aiAgentAliasRepository.save(aiAgentAlias);
    }

    public AiAgentToolResource createAiAgentToolResource(AiAgentTool tool, String uuid, String name, String description,
                                                         String toolResourceJson) {
        AiAgentToolResource resource = AiAgentToolResource.builder().guid(uuid).tool(tool).name(name).description(
                description).toolResourceJson(toolResourceJson).build();
        return _resourceRepository.save(resource);
    }

    public AiAgentGuardrail saveAiAgentGuardrail(AiAgentProviderAccount aiAgentProviderAccount, String idpAccountId) {

        AiAgentGuardrail aiAgentGuardrail = AiAgentGuardrail.builder()
                .guid(GuidUtil.createAIAgentGuardrailGuid())
                .externalId("test_external_id")
                .idpAccountId(idpAccountId)
                .aiAgentProviderAccount(aiAgentProviderAccount)
                .versionString("1.0")
                .versionInt(1).name("test_guardrail").description("test_description").guardrailJson(
                        "{\"name\": \"Test Guardrail\", \"description\":" +
                        " \"This is a test guardrail configuration\"}")
                .build();

        aiAgentGuardrail.setCreatedByUserId(COMMON_USER_NAME);

        return _aiAgentGuardrailRepository.save(aiAgentGuardrail);
    }

    public AiAgentGuardrailAssociation saveAiAgentGuardrailAssociation(AiAgentGuardrail aiAgentGuardrail,
                                                                       Integer aiAgentVersionUid) {

        AiAgentGuardrailAssociation aiAgentGuardrailAssociation = AiAgentGuardrailAssociation.builder()
                .guid(GuidUtil.createAIAgentGuardrailAssociationGuid())
                .guardrail(aiAgentGuardrail)
                .relatedEntityType(AiRegistryEntityType.VERSION)
                .relatedEntityUid(aiAgentVersionUid)
                .build();

        return _aiAgentGuardrailAssociationRepository.save(aiAgentGuardrailAssociation);
    }

    public AiAgentToolAssociation saveAiAgentToolAssociation(AiAgentTool aiAgentTool, Integer aiAgentUid) {

        AiAgentToolAssociation aiAgentToolAssociation = AiAgentToolAssociation.builder().guid(
                GuidUtil.createAIAgentToolAssociationGuid()).tool(aiAgentTool).relatedEntityType(
                AiRegistryEntityType.VERSION).relatedEntityUid(aiAgentUid).build();

        return _agentToolAssociationRepository.save(aiAgentToolAssociation);
    }

    private AiAgentProviderAccount createAiAgentProvider(int zeroBasedCounter) {
        AiAgentProviderAccount aiAgentProviderAccount = new AiAgentProviderAccount();
        aiAgentProviderAccount.setIdpAccountId(ACCOUNT_ID);
        String aiAgentProviderGuid = GuidUtil.createAIAgentProviderGuid();
        aiAgentProviderAccount.setUid(zeroBasedCounter + 1);
        aiAgentProviderAccount.setGuid(aiAgentProviderGuid);
        aiAgentProviderAccount.setProviderAccountName(TEST_REGISTRY_ACCOUNT + zeroBasedCounter);
        aiAgentProviderAccount.setProviderAccountStatus(AiAgentProviderAccountStatus.CONNECTED);
        aiAgentProviderAccount.setProviderType(isBoomiOrBedrockProvider(zeroBasedCounter));
        aiAgentProviderAccount.setAuthSchema(AiProviderAuthSchema.BOOMIAPITOKEN);
        String secretName = COMMON_ACCOUNT_ID.concat(DELIMITER.concat(String.valueOf(aiAgentProviderGuid)));
        aiAgentProviderAccount.setCredentialsKey(secretName);
        aiAgentProviderAccount.setProviderMetadata(PROVIDER_ACCOUNT_METADATA);
        aiAgentProviderAccount.setRegion(US_EAST_1);
        aiAgentProviderAccount.setExternalProviderAccountId("external-provider-account-id" + zeroBasedCounter);
        aiAgentProviderAccount.setExternalProviderAccountName("external-provider-account-name" + zeroBasedCounter);
        setAuditFields(aiAgentProviderAccount);

        _aiAgentProviderAccountRepository.save(aiAgentProviderAccount);

        if (!hasAgents(zeroBasedCounter)) {
            return aiAgentProviderAccount;
        }

        AiAgent aiAgent = new AiAgent();
        aiAgent.setGuid(GuidUtil.createAIAgentGuid());
        aiAgent.setAiAgentProviderAccount(aiAgentProviderAccount);
        aiAgent.setExternalId(AI_AGENT_EXTERNAL_ID_1);
        aiAgent.setIdpAccountId(ACCOUNT_ID);
        aiAgent.setIsDeleted(false);
        _aiAgentRepository.save(aiAgent);

        AiAgent aiAgent2 = new AiAgent();
        aiAgent2.setGuid(GuidUtil.createAIAgentGuid());
        aiAgent2.setAiAgentProviderAccount(aiAgentProviderAccount);
        aiAgent2.setExternalId(AI_AGENT_EXTERNAL_ID_2);
        aiAgent2.setIdpAccountId(ACCOUNT_ID);
        aiAgent2.setIsDeleted(false);
        _aiAgentRepository.save(aiAgent2);

        AiAgent aiAgent3 = new AiAgent();
        aiAgent3.setGuid(GuidUtil.createAIAgentGuid());
        aiAgent3.setAiAgentProviderAccount(aiAgentProviderAccount);
        aiAgent3.setExternalId(AI_AGENT_EXTERNAL_ID_3);
        aiAgent3.setIdpAccountId(ACCOUNT_ID);
        aiAgent3.setIsDeleted(false);
        _aiAgentRepository.save(aiAgent3);

        return aiAgentProviderAccount;
    }

    public static AiAgentProviderType isBoomiOrBedrockProvider(int counter) {
        return counter % 2 == 0 ? AiAgentProviderType.BOOMI : AiAgentProviderType.AWS_BEDROCK;
    }

    public static boolean hasAgents(int counter) {
        return counter % 2 == 0;
    }

    public AiAgentTool saveAiAgentTool(AiAgentTestBuilder.ToolSetupParams params) {
        AiAgentTool tool = AiAgentTool.builder()
                .guid(GuidUtil.createAIAgentToolGuid())
                .name(params.getName())
                .description(params.getDescription())
                .externalId(params.getExternalId())
                .idpAccountId(params.getIdpAccountId())
                .aiAgentProviderAccount(params.getProviderAccount())
                .status(params.getStatus())
                .toolType(params.getType())
                .createdTime(AuditUtilImpl.getCurrentTime())
                .updatedAtProviderTime(GeneralUtil.tmStmpFromInst(params.getUpdatedAt()))
                .build();
        return _aiAgentToolRepository.save(tool);
    }

    public AiAgent createAiAgent(AiAgentTestBuilder.AgentSetupParams params) {
        AiAgent aiAgent = AiAgent.builder()
                .guid(GuidUtil.createAIAgentGuid())
                .externalId(params.getExternalId())
                .idpAccountId(params.getIdpAccountId())
                .aiAgentProviderAccount(params.getProviderAccount())
                .isDeleted(false)
                .build();
        return _aiAgentRepository.save(aiAgent);
    }

    public AiAgentVersion createAiAgentVersion(AiAgent agent, AiAgentTestBuilder.VersionSetupParams params) {

        AiAgentVersion aiAgentVersion = AiAgentVersion.builder()
                .guid(GuidUtil.createAIAgentVersionGuid())
                .externalId(params.getExternalId())
                .name(params.getName())
                .description(params.getDescription())
                .agentStatus(params.getAgentStatus())
                .updatedAtProviderTime(GeneralUtil.tmStmpFromInst(params.getUpdatedAt()))
                .createdInRegistry(params.isCreatedInRegistry())
                .createdByOrigin(params.getCreatedByOrigin())
                .versionString(params.getVersionString())
                .isDeleted(params.isDeleted())
                .updatedAtProviderTime(GeneralUtil.tmStmpFromInst(params.getUpdatedAt()))
                .updatedByOrigin(params.getUpdatedByOrigin())
                .createdTime(AuditUtilImpl.getCurrentTime())
                .agent(agent)
                .build();
        AiAgentVersion version = _aiAgentVersionRepository.save(aiAgentVersion);
        if (params.getInstructions() != null) {
            AiAgentLargeTextContent instruction = AiAgentLargeTextContent.builder()
                    .content(params.getInstructions())
                    .relatedEntityType(AiRegistryEntityType.VERSION.name())
                    .relatedEntityUid(version.getUid())
                    .build();
            _largeTextRepo.save(instruction);
        }
        return version;
    }


    public AiAgentAlias createVersionAlias(AiAgentVersion version, AiAgentTestBuilder.AliasSetupParams params) {
        AiAgentAlias aiAgentAlias = AiAgentAlias.builder()
                .guid(GuidUtil.createAIAgentAliasGuid())
                .externalId(params.getExternalId())
                .name(params.getName())
                .isDeleted(false)
                .description(params.getDescription())
                .updatedAtProviderTime(GeneralUtil.tmStmpFromInst(params.getUpdatedAt()))
                .agentVersion(version)
                .createdByUserId(COMMON_USER_NAME)
                .createdTime(CREATED_DATE)
                .modifiedByUserId(COMMON_USER_NAME)
                .modifiedTime(MODIFIED_DATE)
                .build();

        return _aiAgentAliasRepository.save(aiAgentAlias);
    }

    public AiAgentToolResource createAiAgentToolResource(AiAgentTool tool, AiAgentTestBuilder.ToolResourceParams params) {
        AiAgentToolResource aiAgentToolResource = AiAgentToolResource.builder()
                .guid(GuidUtil.createAIAgentToolResourceGuid())
                .name(params.getName())
                .description(params.getDescription())
                .externalId(params.getExternalId())
                .status(params.getStatus())
                .updatedAtProviderTime(GeneralUtil.tmStmpFromInst(params.getUpdatedAt()))
                .resourceType(params.getType())
                .tool(tool)
                .build();
        return _resourceRepository.save(aiAgentToolResource);
    }

    public void createAiAgentToolAssociation(AiAgentTool tool, AiAgentVersion version) {
        AiAgentToolAssociation aiAgentToolAssociation = AiAgentToolAssociation.builder()
                .guid(GuidUtil.createAIAgentToolAssociationGuid())
                .tool(tool)
                .relatedEntityType(AiRegistryEntityType.VERSION)
                .relatedEntityUid(version.getUid())
                .build();
        _agentToolAssociationRepository.save(aiAgentToolAssociation);
    }

    public AiAgentTag createAiAgentTag(AiAgentTestBuilder.TagParams params) {
        AiAgentTag aiAgentTag = AiAgentTag.builder()
                .guid(params.getGuid())
                .idpAccountId(params.getIdpAccountId())
                .key(params.getKey())
                .value(params.getValue())
                .build();
        return _aiAgentTagRepository.save(aiAgentTag);
    }

    public AiAgentTagAssociation createAiAgentTagAssociation(AiAgentTestBuilder.TagAssociationParams params) {
        AiAgentTagAssociation aiAgentTagAssociation = AiAgentTagAssociation.builder()
                .guid(params.getGuid())
                .tagUid(params.getTagUid())
                .relatedEntityType(params.getRelatedEntityType())
                .relatedEntityUid(params.getRelatedEntityUid())
                .build();
        return _aiAgentTagAssociationRepository.save(aiAgentTagAssociation);
    }


    public AiAgentLlm saveAiAgentLlm(AiAgentProviderAccount account) {
        AiAgentLlm aiAgentLlm = AiAgentLlm.builder()
                .idpAccountId(account.getIdpAccountId())
                .guid(GuidUtil.createLlmGuid())
                .externalId("agent-llm-externalid")
                .name("agent-llm-name")
                .description("agent-llm-description")
                .versionString("agent-llm-version")
                .providerAccount(account)
                .versionInt(1)
                .build();
        return _aiAgentLlmRepository.save(aiAgentLlm);
    }

    public AiAgentLlmAssociation saveAiAgentLlmAssociation(AiAgentLlm aiAgentLlm,
                                                           Integer relatedEntityUid,
                                                           AiRegistryEntityType entityType) {
        AiAgentLlmAssociation aiAgentLlmAssociation = AiAgentLlmAssociation.builder()
                .llm(aiAgentLlm)
                .guid(GuidUtil.createLlmAssociationGuid())
                .relatedEntityUid(relatedEntityUid)
                .relatedEntityType(entityType)
                .build();
        return _aiAgentLlmAssociationRepository.save(aiAgentLlmAssociation);
    }

    public AiAgentLlm saveAiAgentLlm(AiAgentTestBuilder.LlmSetupParams aiAgentLlmParams) {
        AiAgentLlm aiAgentLlm = AiAgentLlm.builder()
                .guid(GuidUtil.createLlmGuid())
                .externalId(aiAgentLlmParams.getExternalId())
                .name(aiAgentLlmParams.getName())
                .description(aiAgentLlmParams.getDescription())
                .providerAccount(aiAgentLlmParams.getProviderAccount())
                .versionString(aiAgentLlmParams.getVersionString())
                .build();
        return _aiAgentLlmRepository.save(aiAgentLlm);
    }

    private static void setAuditFields(Auditable auditable) {
        auditable.setCreatedTime(AuditUtilImpl.getCurrentTime());
        auditable.setCreatedByUserId("createdByUser");
        auditable.setModifiedTime(AuditUtilImpl.getCurrentTime());
        auditable.setModifiedByUserId("modifiedByUser");
    }

    public void cleanupDatabases() {
        _aiAgentLatestSyncRepository.deleteAll();
        _syncUserAuditRepository.deleteAll();
        _aiAgentGuardrailAssociationRepository.deleteAll();
        _aiAgentLlmAssociationRepository.deleteAll();
        _agentToolAssociationRepository.deleteAll();
        _resourceRepository.deleteAll();
        _largeTextRepo.deleteAll();
        _aiAgentAliasRepository.deleteAll();
        _aiAgentToolRepository.deleteAll();
        _aiAgentVersionRepository.deleteAll();
        _aiAgentRepository.deleteAll();
        _aiAgentLlmRepository.deleteAll();
        _aiAgentGuardrailRepository.deleteAll();
        _aiAgentListingRepository.deleteAll();
    }

    public AiAgentGuardrail createGuardrail(AiAgentTestBuilder.GuardrailSetupParams parms) {
        AiAgentGuardrail guardrail;
        guardrail = AiAgentGuardrail.builder()
                .guid(parms.getGuid())
                .externalId(parms.getExternalId())
                .name(parms.getName())
                .description(parms.getDescription())
                .aiAgentProviderAccount(parms.getProviderAccount())
                .versionString(parms.getVersionString())
                .updatedAtProviderTime(GeneralUtil.tmStmpFromInst(parms.getUpdatedAt()))
                .createdTime(AuditUtilImpl.getCurrentTime())
                .updatedByOrigin(parms.getUpdatedByOrigin())
                .build();
        return _aiAgentGuardrailRepository.save(guardrail);
    }

    public AiAgentToolAssociation saveAiAgentToolTaskAssociation(AiAgentTool tool, AiAgentTask aiAgentTask) {

        AiAgentToolAssociation aiAgentToolAssociation = AiAgentToolAssociation.builder().guid(
                        GuidUtil.createAIAgentToolAssociationGuid())
                .relatedEntityType(AiRegistryEntityType.TASK)
                .tool(tool).relatedEntityUid(aiAgentTask.getUid()).build();

        return _agentToolAssociationRepository.save(aiAgentToolAssociation);
    }
    public AiAgentTask saveAiAgentTask(AiAgentTestBuilder.TaskSetupParams aiAgentTaskParams) {
        AiAgentTask aiAgentTask = AiAgentTask.builder()
                .guid(GuidUtil.createLlmGuid())
                .externalId(aiAgentTaskParams.getExternalId())
                .name(aiAgentTaskParams.getName())
                .description(aiAgentTaskParams.getDescription())
                .versionString(aiAgentTaskParams.getVersionString())
                .build();
        return _aiAgentTaskRepository.save(aiAgentTask);
    }

    public void saveAiAgentTaskAssociation(AiAgentVersion version, AiAgentTask task) {

        AiAgentTaskAssociation aiAgentTaskAssociation = AiAgentTaskAssociation.builder()
                .guid(GuidUtil.createAIAgentTaskAssociationGuid())
                .task(task)
                .relatedEntityType(AiRegistryEntityType.VERSION)
                .relatedEntityUid(version.getUid())
                .build();
        _agentTaskAssociationRepository.save(aiAgentTaskAssociation);
    }


    public void deleteAllAiAgentToolTaskAssociation() {

        _agentToolAssociationRepository.deleteAll();
    }
    public void deleteAllLargeText() {
        _largeTextRepo.deleteAll();
    }
    public void createListings(AiAgentVersion aiAgentVersionOne,String providerAccountGuid) {
        // Create test data
        AiAgentListing testAgent = new AiAgentListing();
        testAgent.setId(new VersionAliasIdKey(11, 70));
        testAgent.setAgentGuid(UUID.randomUUID().toString());
        testAgent.setAliasName("Test Agent");
        testAgent.setDescription("Listing agent test Description");
        testAgent.setProviderType(AiAgentProviderType.AWS_BEDROCK.name());
        testAgent.setAgentStatus("ACTIVE");
        testAgent.setProviderAccountGuId(providerAccountGuid);
        testAgent.setAgentIsDeleted(false);
        testAgent.setIdpAccountId("boomi-internal");
        testAgent.setModifiedTime(Timestamp.valueOf(LocalDateTime.now()));
        _aiAgentListingRepository.save(testAgent);

        AiAgentListing testAgent2 = new AiAgentListing();
        testAgent2.setId(new VersionAliasIdKey(2, aiAgentVersionOne.getUid()));
        testAgent2.setAgentGuid(UUID.randomUUID().toString());
        testAgent2.setAliasName("Test Agent2");
        testAgent2.setVersionName(aiAgentVersionOne.getName());
        testAgent2.setVersionGuid(aiAgentVersionOne.getGuid());
        testAgent2.setProviderAccountName("creating simple test agent for arithmetic operation");
        testAgent2.setProviderType(AiAgentProviderType.AWS_BEDROCK.name());
        testAgent2.setProviderAccountGuId(providerAccountGuid);
        testAgent2.setAgentStatus("ACTIVE");
        testAgent2.setAgentIsDeleted(false);
        testAgent2.setIdpAccountId("boomi-internal");
        testAgent2.setModifiedTime(Timestamp.valueOf(LocalDateTime.now()));
        _aiAgentListingRepository.save(testAgent2);

        AiAgentListing testAgent3 = new AiAgentListing();
        testAgent3.setId(new VersionAliasIdKey(3, 3));
        testAgent3.setAgentGuid(UUID.randomUUID().toString());
        testAgent3.setAliasName("alias test3");
        testAgent3.setProviderAccountGuId(providerAccountGuid);
        testAgent3.setProviderAccountName("Test Agent with Guardrail");
        testAgent3.setProviderType(AiAgentProviderType.BOOMI.name());
        testAgent3.setAgentStatus("ACTIVE");
        testAgent3.setAgentIsDeleted(false);
        testAgent3.setIdpAccountId("boomi-internal");
        testAgent3.setModifiedTime(Timestamp.valueOf(LocalDateTime.now()));
        _aiAgentListingRepository.save(testAgent3);
    }


}
