spring:
  profiles:
    active: test,dev
  webflux:
    base-path: /service/ai-agent-registry
  application:
    name: ai-agent-registry-service
  aws:
    region: us-east-1
    secondaryregion: us-west-2

  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: http://localhost:8081/auth/.well-known/jwks.json
          issuer-uri: http://localhost:8081

  liquibase:
    enabled: false

  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:ai_agent_registry_service;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false;mode=PostgreSQL;
    username: test-user
    password: pw
  jpa:
    hibernate:
      ddl-auto: create
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
    show-sql: false
  sql:
    init:
      mode: always
      schema-locations: classpath:sql/schema.sql
    h2:
      console:
        enabled: true

server:
  port: 3039


boomi:
  services:
    service-name: '@project.artifactId@'
    timestream:
      max_rows: 1000
      agent_metrics_table_name: "ai_agent_registry_service_metrics"
      database_name: TestDb
      region: us-east-1
    monitor:
      user: user
      pass: password

    aiagentregistry:
      write:
        non:
          summary:
            logs: true
      use:
        real:
          oam:
            client: true
      service:
        assume:
          role:
            session:
              name: BoomiActOamLinkSession
        account:
          role: ai-registry-sandbox-ai-agent-registry-sa-role-us-east-1
      aws:
        metrics:
          stream:
            in:
              all:
                regions: true
      s3:
        bucket: boomi-ai-agent-registry-app-data-test-us-east-1
      monitoring:
        account:
          id: ************
      customer:
        aws:
          oam:
            role:
              prefix: Boomi-ACT-oam-customer-role
              policy:
                prefix: Boomi-ACT-oam-access-policy
          bedrock:
            role:
              prefix: Boomi-ACT-bedrock-customer-role
              policy:
                prefix: Boomi-ACT-bedrock-access-policy
          role:
            prefix: Boomi-ACT-bedrock-customer-role
            assume:
              # 1 hour (max allowed when using role chaining to assume another role)
              session_duration_seconds: 3600
            policy:
              prefix: Boomi-ACT-bedrock-access-policy
      local:
        oam:
          sink:
            arn:
              service: true
        auth: true
      garden:
        apiUrl:
        jwtUrl:
      pattern:
        externalLink:
          AGENT:
            AWS_BEDROCK: https://{REGION}.console.aws.amazon.com/bedrock/home?region={REGION}#/agents/{AGENT_ID}
            BOOMI: https://qa.boomi.com/BoomiAI.html#ai;accountId={ACCOUNT_ID}
          VERSION:
            AWS_BEDROCK: https://{REGION}.console.aws.amazon.com/bedrock/home?region={REGION}#/agents/{AGENT_ID}/versions/{AGENT_VERSION}
            BOOMI: https://qa.boomi.com/BoomiAI.html#ai;accountId={ACCOUNT_ID}
          ALIAS:
            AWS_BEDROCK: https://{REGION}.console.aws.amazon.com/bedrock/home?region={REGION}#/agents/{AGENT_ID}/alias/{AGENT_ALIAS_ID}
com:
  boomi:
    aiagentregistry:
      environment: test
info:
  app:
    name: '@project.name@'
    version: '@project.version@'
    build: '@buildNumber@'

management:
  endpoints:
    web:
      base-path: /monitor
      exposure:
        include: health,loggers

  endpoint:
    health:
      show-details: always
    loggers:
      enabled: false

graphql:
  security:
    allowAnonymousAccess: true

api:
  boomi:
    platform:
      url: http://localhost:8081

aws:
  secretsmanager:
    local:
      enabled: true
  s3:
    local:
      enabled: true

logging:
  level:
    software.amazon.awssdk: OFF
    software.amazon.awssdk.requestId: OFF
    # Show actual sql query parameters
    org.hibernate.orm.jdbc.bind: OFF
sync:
  type: AUTO
  queue:
    url: http://host.docker.internal:4566/************/ai-agent-registry-sandbox-account-sync-queue
    region: us-east-1
    isLocal: true
