// Copyright (c) 2024 Boom<PERSON>, LP
package com.boomi.aiagentregistry.servlet;

import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 */

@Component
public class ApiClient {
    private final WebClient.Builder _webClientBuilder;


    public ApiClient(WebClient.Builder webClientBuilder) {
        _webClientBuilder = webClientBuilder;
    }

    public <T> Flux <T> get(RequestBuilder requestBuilder, Class<T> responseTYpe){
        return _webClientBuilder.build()
                .get()
                .uri(requestBuilder.buildUrl())
                .headers(httpHeaders -> requestBuilder.getHeaders().forEach(httpHeaders::add))
                .retrieve()
                .bodyToFlux(responseTYpe);
    }

    public <T> Flux<T> post(RequestBuilder requestBuilder, Class<T> responseTYpe){
        return _webClientBuilder.build()
                .post()
                .uri(requestBuilder.buildUrl())
                .headers(httpHeaders -> requestBuilder.getHeaders().forEach(httpHeaders::add))
                .bodyValue(requestBuilder.getBody())
                .retrieve()
                .bodyToFlux(responseTYpe);

    }
}

