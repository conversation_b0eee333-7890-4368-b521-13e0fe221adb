// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.resolver.query;

import graphql.schema.DataFetchingEnvironment;
import jakarta.persistence.criteria.Predicate;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.repo.AiAgentListingRepository;
import com.boomi.aiagentregistry.util.UserUtil;
import com.boomi.graphql.server.api.ResolverCallback;
import com.boomi.graphql.server.schema.resolvers.AiAgentRegistryAgentMetricResolver;
import com.boomi.graphql.server.schema.types.AiAgentListing;
import com.boomi.graphql.server.schema.types.AiAgentRegistryAgentMetric;
import com.boomi.util.StringUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;

import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class AiAgentRegistryInvocationsMetricsResolverImpl implements AiAgentRegistryAgentMetricResolver {
    private final UserUtil _userUtil;
    private final AiAgentListingRepository _aiAgentListingRepository;
    private final ObjectMapper _objectMapper;

    private static final String ALIAS_EXT_ID_FIELD = "aliasExternalId";
    private static final String AGENT_EXT_ID_FIELD = "agentExternalId";
    private static final String IDP_ACCOUNT_ID_FIELD = "idpAccountId";
    private static final String PROVIDER_TYPE_FIELD = "providerType";
    private static final String PROVIDER_ACCOUNT_GUID_FIELD = "providerAccountGuId";

    private static final String KEY_SEPARATOR = "|";
    private static final int INDEX_ZERO = 0;
    private static final int INDEX_ONE = 1;
    private static final int INDEX_TWO = 2;
    private static final int INDEX_THREE = 3;
    private static final int INDEX_FOUR = 4;

    @Override
    public void bulkAiAgentListing(Collection<@NotNull AiAgentRegistryAgentMetric> input,
            ResolverCallback<@NotNull AiAgentRegistryAgentMetric, AiAgentListing> callback,
            DataFetchingEnvironment dfe) {
        if (input == null || input.isEmpty()) {
            callback.complete();
            return;
        }

        String idpAccountId = _userUtil.getAccountId(dfe);

        // Build a set of composite keys needed for lookup
        Set<List<String>> compositeLookupKeys = buildCompositeLookupKeys(input, idpAccountId);

        // Generate combined OR specification for querying
        Specification<com.boomi.aiagentregistry.entity.AiAgentListing> combinedSpec = compositeLookupKeys.stream()
                .map(k -> getSpecification(k.get(INDEX_ZERO), k.get(INDEX_ONE), k.get(INDEX_TWO),
                        k.get(INDEX_THREE), k.get(INDEX_FOUR)))
                .reduce(Specification::or)
                .orElse(null);

        List<com.boomi.aiagentregistry.entity.AiAgentListing> matchedListings = combinedSpec != null
                ? _aiAgentListingRepository.findAll(combinedSpec)
                : Collections.emptyList();

        // Build a lookup map with composite keys
        Map<String, com.boomi.aiagentregistry.entity.AiAgentListing> metadataMap = matchedListings.stream()
                .collect(Collectors.toMap(
                        listing -> joinKey(
                                listing.getAliasExternalId(),
                                listing.getAgentExternalId(),
                                listing.getIdpAccountId(),
                                String.valueOf(listing.getProviderType()),
                                listing.getProviderAccountGuId()
                        ),
                        Function.identity(),
                        // Keep first if duplicate
                        (a, b) -> a
                ));

        // Map results back to input metrics
        for (AiAgentRegistryAgentMetric metric : input) {
            AiAgentListing inputMetadata = metric.getAiAgentListing();
            String key = joinKey(
                    inputMetadata.getAliasExternalId(),
                    inputMetadata.getAgentExternalId(),
                    idpAccountId,
                    String.valueOf(inputMetadata.getProviderType()),
                    inputMetadata.getProviderAccountGuId()
            );

            com.boomi.aiagentregistry.entity.AiAgentListing dbMetadata = metadataMap.get(key);

            AiAgentListing response = dbMetadata != null
                    ? mapDBMetadataToResponse(dbMetadata)
                    : inputMetadata;

            metric.setAiAgentListing(response);
            callback.set(metric, response);
        }

        callback.complete();
    }

    private AiAgentListing mapDBMetadataToResponse(com.boomi.aiagentregistry.entity.AiAgentListing dbMetadata) {
        AiAgentListing response = _objectMapper.convertValue(dbMetadata, AiAgentListing.class);

        // Map the GUID fields
        response.setVersionId(dbMetadata.getVersionGuid());
        response.setAgentId(dbMetadata.getAgentGuid());
        response.setAliasId(dbMetadata.getAliasGuid());

        return response;
    }

    // Utility to join fields into a composite key
    private static String joinKey(String... parts) {
        return String.join(KEY_SEPARATOR, parts);
    }

    public static Specification<com.boomi.aiagentregistry.entity.AiAgentListing> getSpecification(
            String aliasExternalId,
            String agentExternalId,
            String idpAccountId,
            String providerType,
            String providerAccountGuid) {

        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (!StringUtil.isBlank(aliasExternalId)) {
                predicates.add(cb.equal(root.get(ALIAS_EXT_ID_FIELD), aliasExternalId));
            }

            predicates.add(cb.equal(root.get(AGENT_EXT_ID_FIELD), agentExternalId));
            predicates.add(cb.equal(root.get(IDP_ACCOUNT_ID_FIELD), idpAccountId));
            predicates.add(cb.equal(root.get(PROVIDER_TYPE_FIELD), providerType));
            predicates.add(cb.equal(root.get(PROVIDER_ACCOUNT_GUID_FIELD), providerAccountGuid));

            return cb.and(predicates.toArray(new Predicate[INDEX_ZERO]));
        };
    }

    @VisibleForTesting
    protected Set<List<String>> buildCompositeLookupKeys(Collection<AiAgentRegistryAgentMetric> input,
            String idpAccountId) {

        return input.stream()
                .map(metric -> {
                    AiAgentListing listing = metric.getAiAgentListing();
                    return Arrays.asList(
                            listing.getAliasExternalId(),
                            listing.getAgentExternalId(),
                            idpAccountId,
                            String.valueOf(listing.getProviderType()),
                            listing.getProviderAccountGuId()
                    );
                })
                .collect(Collectors.toSet());
    }
}