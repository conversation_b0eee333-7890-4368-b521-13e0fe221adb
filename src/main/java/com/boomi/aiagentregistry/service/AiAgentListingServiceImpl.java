// Copyright (c) 2025 Boomi, LP.
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.aop.IdpAccountFilter;
import com.boomi.aiagentregistry.constant.ErrorMessages;
import com.boomi.aiagentregistry.constant.FieldConstant;
import com.boomi.aiagentregistry.entity.AiAgentListing;
import com.boomi.aiagentregistry.model.AgentStatusCount;
import com.boomi.aiagentregistry.repo.AiAgentListingRepository;
import com.boomi.aiagentregistry.repo.AiAgentLlmAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentLlmRepository;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;
import com.boomi.aiagentregistry.repo.AiAgentTagAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentTagRepository;
import com.boomi.aiagentregistry.repo.AiAgentToolAssociationRepository;
import com.boomi.aiagentregistry.search.SearchByProviderTypeStrategy;
import com.boomi.aiagentregistry.search.SearchStrategy;
import com.boomi.aiagentregistry.util.SearchUtil;
import com.boomi.aiagentregistry.util.UserUtilImpl;
import com.boomi.graphql.server.schema.types.AiAgentFilterLookup;
import com.boomi.graphql.server.schema.types.AiAgentListingAllFiltersResponse;
import com.boomi.graphql.server.schema.types.AiAgentListingFilterQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiAgentRegistryTrustLevel;
import com.boomi.graphql.server.schema.types.AiAgentTag;
import com.boomi.graphql.server.schema.types.AiAgentsListingQueryInput;
import com.boomi.graphql.server.schema.types.AiAgentsListingQueryResponse;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import com.boomi.graphql.server.schema.types.LlmInfo;
import com.boomi.graphql.server.servlet.ErrorUtil;
import com.boomi.graphql.server.servlet.ResolverUtil;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

import static com.boomi.aiagentregistry.mapper.AiAgentFilterDropdownMapper.DROP_DOWN_FILTER_MAPPER;
import static com.boomi.aiagentregistry.mapper.AiAgentListingMapper.AI_AGENT_LISTING_MAPPER;
import static com.boomi.aiagentregistry.mapper.AiAgentStatusCountMapper.AGENT_STATUS_COUNT_MAPPER;
import static com.boomi.aiagentregistry.mapper.AiAgentTagMapper.AI_AGENT_TAG_MAPPER;
import static java.util.stream.Collectors.toList;

@Slf4j
@Service
public class AiAgentListingServiceImpl implements IAiAgentListingService {

    private final SearchUtil _searchUtil;
    private final AiAgentProviderAccountRepository _aiAgentProviderAccountRepository;
    private final AiAgentTagRepository _aiAgentTagRepository;
    private final AiAgentLlmRepository _aiAgentLlmRepository;
    private final AiAgentListingRepository _aiAgentListingRepository;
    private final AiAgentTagAssociationRepository _aiAgentTagAssociationRepository;

    private final AiAgentLlmAssociationRepository _aiAgentLlmAssociationRepository;
    private final AiAgentToolAssociationRepository _aiAgentToolAssociationRepository;

    private static final int DEFAULT_START_INDEX = 0;
    private static final int DEFAULT_END_INDEX = 10;
    private final UserUtilImpl userUtilImpl;

    public AiAgentListingServiceImpl(SearchUtil searchUtil,
            AiAgentProviderAccountRepository aiAgentProviderAccountRepository,
            AiAgentTagRepository aiAgentTagRepository, AiAgentLlmRepository aiAgentLlmRepository,
            AiAgentListingRepository aiAgentListingRepository,
            AiAgentTagAssociationRepository aiAgentTagAssociationRepository,
            AiAgentLlmAssociationRepository aiAgentLlmAssociationRepository,
            AiAgentToolAssociationRepository aiAgentToolAssociationRepository, UserUtilImpl userUtilImpl) {
        _searchUtil = searchUtil;
        _aiAgentProviderAccountRepository = aiAgentProviderAccountRepository;
        _aiAgentTagRepository = aiAgentTagRepository;
        _aiAgentLlmRepository = aiAgentLlmRepository;
        _aiAgentListingRepository = aiAgentListingRepository;
        _aiAgentTagAssociationRepository = aiAgentTagAssociationRepository;
        _aiAgentLlmAssociationRepository = aiAgentLlmAssociationRepository;
        _aiAgentToolAssociationRepository = aiAgentToolAssociationRepository;
        this.userUtilImpl = userUtilImpl;
    }

    @Transactional
    @Override
    /**
     *  This will return required data for filter dropdowns. All tags ,llms,provider accounts and provider types
     */
    public CompletionStage<AiAgentListingAllFiltersResponse> allAiAgentListingFilters(
            AiAgentListingFilterQueryInput input, DataFetchingEnvironment dfe) {

        CompletableFuture<AiAgentListingAllFiltersResponse> completion = new CompletableFuture<>();
        try {
            String idpAccountId = userUtilImpl.getAccountId(dfe);
            List<AiAgentProviderType> providerTypes = _aiAgentProviderAccountRepository.getProviderTypes(idpAccountId);
            List<AiAgentRegistryTrustLevel> trustLevels =
                    _aiAgentListingRepository.getTrustLevel(idpAccountId);
            List<AiAgentFilterLookup> accounts =
                    DROP_DOWN_FILTER_MAPPER.toGraphQLTypeList(_aiAgentProviderAccountRepository
                            .getAccounts(idpAccountId));
            String providerId = (input != null) ? input.getProviderAccountId() : null;

            Set<String> llms = getLlms(providerId,idpAccountId);

            Set<AiAgentFilterLookup> tags = DROP_DOWN_FILTER_MAPPER.toGraphQLTypeSet(
                    getTags(idpAccountId, providerId));

            AiAgentListingAllFiltersResponse response = new AiAgentListingAllFiltersResponse();
            // returning accounts if the request is not for a particular provider account
            if (providerId == null) {
                response.setAccount(accounts);
            }

            response.setProvider(providerTypes);
            response.setModel(llms.stream().toList());
            response.setTag(tags.stream().toList());
            response.setTrustLevel(trustLevels);
            completion.complete(response);
            return completion;
        } catch (Exception e) {
            log.error(AiAgentRegistryErrorCode.FETCH_AI_AGENT_FILTER_ERROR.getDetail(), e);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.FETCH_AI_AGENT_FILTER_ERROR);
            completion.complete(null);
            return completion;
        }
    }

    private Set<String> getLlms(String providerId, String idpAccountId) {
        return providerId != null ? _aiAgentLlmRepository.getLlmsByProviderAccountGuid(idpAccountId, providerId) :
                _aiAgentLlmRepository.getLlms(idpAccountId);
    }

    @Transactional(readOnly = true)
    @Override
    @IdpAccountFilter

    /**
     *  This will return all ai agents for ACT landing page
     */
    public CompletableFuture<AiAgentsListingQueryResponse> getAiAgentListings(AiAgentsListingQueryInput input,
            DataFetchingEnvironment dfe) {
        CompletableFuture<AiAgentsListingQueryResponse> completion = new CompletableFuture<>();
        if (_searchUtil.validateInput(input, dfe)) {
            try {
                int offset = input.getOffset() == null ? DEFAULT_START_INDEX : input.getOffset();
                int limit = input.getLimit() == null ? DEFAULT_END_INDEX : input.getLimit();

                validatePagination(offset, limit, dfe);
                Pageable pageable = PageRequest.of(offset, limit);
                // Get all filter strategies. if no filters searchStrategies list wil be empty
                List<SearchStrategy> searchStrategies = _searchUtil.getFilterStrategies(input);
                log.info(" Number of filter searchStrategies: {} ", searchStrategies.size());
                String condition = "AND";
                if (input.getAgentListingFilter() != null && input.getAgentListingFilter().getCondition() != null) {
                    condition = input.getAgentListingFilter().getCondition().name();
                }

                Specification<AiAgentListing> filterSpec = _searchUtil.buildSpecification(searchStrategies,
                        condition, input, dfe);
                log.info(" filterSpec: {} ", filterSpec);
                Page<AiAgentListing> aiAgentsPage = _aiAgentListingRepository.findAll(filterSpec, pageable);
                log.info("aiAgentsPage: {}", aiAgentsPage);

                if (!aiAgentsPage.getContent().isEmpty()) {
                    log.info(" aiAgentsPage content is not empty");

                    AiAgentsListingQueryResponse response =
                            createResponse(aiAgentsPage, dfe);
                    boolean statusCount = searchStrategies.stream()
                            .anyMatch(SearchByProviderTypeStrategy.class::isInstance);
                    if (statusCount) {
                        response.setAiAgentStatusCount(AGENT_STATUS_COUNT_MAPPER.toGraphQLTypeList(
                                getStatusCounts(_aiAgentListingRepository.findAll(filterSpec))));

                        log.info("AiAgentStatusCount {} ", response.getAiAgentStatusCount());
                    }
                    log.info("statusCount {}  and response {} ", statusCount, response);
                    completion.complete(response);
                    return completion;
                }

                log.info("No results found. Returning null");
                completion.complete(null);
                return completion;
            } catch (Exception e) {
                log.error(AiAgentRegistryErrorCode.FETCH_AI_AGENT_ERROR.getDetail(), e);
                ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.FETCH_AI_AGENT_ERROR);
                completion.complete(null);
            }
        } else {
            log.warn(ErrorMessages.INVALID_INPUT);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.INVALID_INPUT);
            completion.complete(null);
        }
        return completion;
    }

    private boolean validatePagination(int offset, int limit, DataFetchingEnvironment dfe) {
        // Validate minimum values
        if (offset < 0) {
            log.warn("Invalid offset value: {}. Offset must be non-negative.", offset);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.INVALID_OFFSET);
            return false;
        }

        if (limit <= 0) {
            log.warn("Invalid limit value: {}. Limit must be greater than zero.", limit);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.INVALID_LIMIT);
            return false;
        }

        return true;
    }

    private AiAgentsListingQueryResponse createResponse(Page<AiAgentListing> aiAgentsPage,
            DataFetchingEnvironment dfe) {

        List<com.boomi.graphql.server.schema.types.AiAgentListing> aiAgentListings = aiAgentsPage.getContent()
                .stream()
                .map(AI_AGENT_LISTING_MAPPER::toAiAgentListing)
                .map(listing -> setRequiredRelatedEntities(listing, dfe))
                .toList();

        // create response
        AiAgentsListingQueryResponse aiAgentsListingQueryResponse = new AiAgentsListingQueryResponse();
        aiAgentsListingQueryResponse.setAiAgentListings(aiAgentListings);
        aiAgentsListingQueryResponse.setNumberOfResults(aiAgentsPage.getTotalElements());
        aiAgentsListingQueryResponse.setCurrentPageSize(aiAgentsPage.getSize());
        return aiAgentsListingQueryResponse;
    }

    private com.boomi.graphql.server.schema.types.AiAgentListing
    setRequiredRelatedEntities(com.boomi.graphql.server.schema.types.AiAgentListing aiAgent,
            DataFetchingEnvironment dfe) {
        Integer aliasUid = _aiAgentListingRepository.findAliasUidByGuid(aiAgent.getAliasId()).orElse(null);
        Integer versionUid = _aiAgentListingRepository.findVersionUidByGuid(aiAgent.getVersionId()).orElse(null);

        //setTags
        if (ResolverUtil.isChildFieldPresent(dfe, FieldConstant.ALIASTAGS) &&
                aliasUid != null && aliasUid > 0 && !aiAgent.isAliasIsDeleted()) {
            aiAgent.setAliasTags(getAssociatedTags(aliasUid, AiRegistryEntityType.ALIAS));
        } else if (ResolverUtil.isChildFieldPresent(dfe, FieldConstant.VERSIONTAGS) && versionUid != null
                && versionUid > 0) {
            aiAgent.setVersionTags(getAssociatedTags(versionUid, AiRegistryEntityType.VERSION));
        }

        //setLLM

        if (ResolverUtil.isChildFieldPresent(dfe, FieldConstant.LLMS)) {
            aiAgent.setLlms(getAssociatedLlms(versionUid));
        }

        //setTools
        if (ResolverUtil.isChildFieldPresent(dfe, FieldConstant.TOOLIDS)) {
            aiAgent.setToolIds(getAssociatedTools(versionUid));
        }

        return aiAgent;
    }

    private List<String> getAssociatedTools(Integer entityId) {
        return _aiAgentToolAssociationRepository.findToolNamesByEntityId(entityId);
    }

    private List<LlmInfo> getAssociatedLlms(Integer entityId) {
        List<com.boomi.aiagentregistry.model.LlmInfo> llmInfos =
                _aiAgentLlmAssociationRepository.findLlmInfoByEntityIdAndType(entityId);

        List<LlmInfo> llmInfoList = new ArrayList<>();
        for (com.boomi.aiagentregistry.model.LlmInfo llm : llmInfos) {
            LlmInfo llmInfo = new LlmInfo();
            llmInfo.setGuid(llm.getGuid());
            llmInfo.setName(llm.getName());
            llmInfoList.add(llmInfo);
        }

        return llmInfoList;
    }

    private List<AiAgentTag> getAssociatedTags(Integer entityId, AiRegistryEntityType entityType) {
        return AI_AGENT_TAG_MAPPER.toAIAgentTagList(_aiAgentTagAssociationRepository
                .findAiAgentTagFromAiAgentTagAssociationByRelatedEntityTypeAndRelatedEntityId(entityType, entityId));
    }

    private Set<com.boomi.aiagentregistry.model.AiAgentFilterDropdown> getTags(String idpAccountId
            , String providerAccountGuId) {
        if (providerAccountGuId == null || providerAccountGuId.isEmpty()) {
            return _aiAgentTagRepository.getTags(idpAccountId);
        }
        // Get tag IDs from both VERSION and ALIAS associations
        Set<Long> versionTagIds = _aiAgentTagRepository.getTagIdsFromVersions(providerAccountGuId);
        Set<Long> aliasTagIds = _aiAgentTagRepository.getTagIdsFromAliases(providerAccountGuId);
        // Combine all tag IDs
        Set<Long> allTagIds = new HashSet<>();
        allTagIds.addAll(versionTagIds);
        allTagIds.addAll(aliasTagIds);
        // If no tags found, return empty set
        if (allTagIds.isEmpty()) {
            return Collections.emptySet();
        }
        // Get tags with names and guids for the collected IDs
        return _aiAgentTagRepository.getTagsByIds(allTagIds, idpAccountId);
    }

    public List<AgentStatusCount> getStatusCounts(List<AiAgentListing> aiAgentListings) {
        // Group by status and count occurrences, filtering out null status
        Map<String, Long> statusCountMap = new HashMap<>();
        for (AiAgentListing agent : aiAgentListings) {
            if (agent.getAgentStatus() != null) {
                statusCountMap.merge(agent.getAgentStatus(), 1L, Long::sum);
            }
        }

        // Convert the map to List<AgentStatusCount>
        return statusCountMap.entrySet().stream()
                .map(entry -> new AgentStatusCount(entry.getKey(), entry.getValue()))
                .collect(toList());
    }
}
