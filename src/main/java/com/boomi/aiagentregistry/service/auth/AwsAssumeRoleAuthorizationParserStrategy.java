// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service.auth;

import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.AwsSessionCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.services.bedrockagent.BedrockAgentAsyncClient;
import software.amazon.awssdk.services.bedrockagent.model.BedrockAgentException;
import software.amazon.awssdk.services.bedrockagent.model.ListAgentsRequest;
import software.amazon.awssdk.services.bedrockagent.model.ListAgentsResponse;
import software.amazon.awssdk.services.sts.model.AssumeRoleResponse;
import com.boomi.aiagentregistry.service.BedrockAssumeRoleService;
import com.boomi.aiagentregistry.servlet.AwsClient;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiProviderAuthSchema;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.apache.commons.lang3.NotImplementedException;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.MAX_RECORD_CONNECTION_TEST;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.SECRET_KEY_PATH_AWS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.INVALID_CREDENTIALS_KEYS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.UNEXPECTED_ERROR_DURING_CONNECTION_VALIDATION;
import static com.boomi.aiagentregistry.util.ValidationUtil.verifyRegionIsNotChanged;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class AwsAssumeRoleAuthorizationParserStrategy implements AuthorizationParserStrategy {

    private final ObjectMapper _objectMapper;
    private final AwsClient _awsClient;
    private final BedrockAssumeRoleService _bedrockAssumeRoleService;

    private static final String NEED_TO_GET_NEW_ACCESS_TOKEN = "Need to get a new access token for AWS account {}";

    public AwsAssumeRoleAuthorizationParserStrategy(ObjectMapper objectMapper, AwsClient awsClient,
            BedrockAssumeRoleService bedrockAssumeRoleService) {
        _objectMapper = objectMapper;
        _awsClient = awsClient;
        _bedrockAssumeRoleService = bedrockAssumeRoleService;
    }

    @Override
    public AiProviderAuthSchema getProviderAuthSchema() {
        return AiProviderAuthSchema.AWS_ASSUME_ROLE;
    }

    @Override
    public Credentials parseCredentials(JsonNode credentialsNode) throws JsonProcessingException {
        return _objectMapper.treeToValue(credentialsNode, AwsCredentials.class);
    }

    @Override
    public boolean validateCredentialFormat(JsonNode credentialsNode) throws JsonProcessingException {
        throw new NotImplementedException("Input mutation should not have credentials property.");
    }

    @Override
    public Mono<Credentials> loadAdditionalProperties(Credentials credentials) {
        return Mono.just(credentials);
    }

    @Override
    public Mono<String> fetchAccountId(JsonNode credentialsNode) {
       throw new IllegalArgumentException("fetchAccountId is not supported for AWS_ASSUME_ROLE");
    }

    @Override
    public boolean validateConnection(JsonNode credentialsNode) {
        return false;
    }

    public Optional<AwsCredentials> validateConnection(String awsAccountId, String awsRegion, String externalId) {
        AssumeRoleResponse assumeRoleResponse = _bedrockAssumeRoleService.getAssumeRoleResponse(awsAccountId,
                awsRegion, externalId);
        AwsCredentialsProvider awsCredentialsProvider = getCredentialsProvider(assumeRoleResponse);
        BedrockAgentAsyncClient bedrockAgentClient = _awsClient.createBedrockAgentAsyncClient(awsCredentialsProvider,
                awsRegion);
        if (validateBedrockConnection(bedrockAgentClient)) {
            AwsCredentials awsCredentials = getAwsCredentials(awsAccountId, awsRegion, externalId, assumeRoleResponse);
            return Optional.of(awsCredentials);
        }
        return Optional.empty();
    }

    @Override
    public boolean requiresRegionCheck() {
        return true;
    }

    @Override
    public Optional<List<AiAgentRegistryErrorCode>> verifyImmutableFieldsAreNotChanged(
            String existingCredentialsString, String newCredentialsString) {
        return verifyRegionIsNotChanged(existingCredentialsString, newCredentialsString, _objectMapper);
    }

    @Override
    public Credentials refreshTempAccessTokenIfExpired(Credentials existingCredentials, int refreshWindowInMinutes) {
        AwsCredentials awsExistingCredentials = (AwsCredentials) existingCredentials;
        AwsCredentials newCredentials = null;
        // Check if temp access token is about to expire
        long currentTime = System.currentTimeMillis();
        long timeUntilExpiration = awsExistingCredentials.getExpirationEpochMilli() - currentTime;
        boolean needRefresh = timeUntilExpiration <= TimeUnit.MINUTES.toMillis(refreshWindowInMinutes);
        if (needRefresh) {
            log.info(NEED_TO_GET_NEW_ACCESS_TOKEN, ((AwsCredentials) existingCredentials).getAwsAccountId());
            // get a new temp access token using Assume Role
            AssumeRoleResponse assumeRoleResponse = _bedrockAssumeRoleService.getAssumeRoleResponse(
                    awsExistingCredentials.getAwsAccountId(), awsExistingCredentials.getAwsRegion(),
                    awsExistingCredentials.getExternalId());
            newCredentials = getAwsCredentials(awsExistingCredentials.getAwsAccountId(),
                    awsExistingCredentials.getAwsRegion(), awsExistingCredentials.getExternalId(), assumeRoleResponse);
            return newCredentials;
        } else {
            return newCredentials;
        }
    }

    private static @NotNull AwsCredentials getAwsCredentials(String awsAccoutnId, String awsRegion, String externalId,
            AssumeRoleResponse assumeRoleResponse) {
        AwsCredentials awsCredentials = new AwsCredentials();
        awsCredentials.setAwsAccountId(awsAccoutnId);
        awsCredentials.setAwsRegion(awsRegion);
        awsCredentials.setAwsAccessKeyId(assumeRoleResponse.credentials().accessKeyId());
        awsCredentials.setAwsSecretAccessKey(assumeRoleResponse.credentials().secretAccessKey());
        awsCredentials.setSessionToken(assumeRoleResponse.credentials().sessionToken());
        awsCredentials.setExternalId(externalId);
        awsCredentials.setExpirationEpochMilli(assumeRoleResponse.credentials().expiration().toEpochMilli());
        return awsCredentials;
    }

    private boolean validateBedrockConnection(BedrockAgentAsyncClient bedrockAgentClient) {
        try (bedrockAgentClient) {
            ListAgentsRequest listAgentsRequest = ListAgentsRequest.builder()
                    .maxResults(MAX_RECORD_CONNECTION_TEST)
                    .build();

            // Execute the request synchronously using get()
            ListAgentsResponse response = bedrockAgentClient.listAgents(listAgentsRequest).get();
            return response.sdkHttpResponse().isSuccessful();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error(UNEXPECTED_ERROR_DURING_CONNECTION_VALIDATION, e);
            return false;
        } catch (ExecutionException e) {
            if (e.getCause() instanceof BedrockAgentException) {
                log.error(INVALID_CREDENTIALS_KEYS, e.getCause());
            } else {
                log.error(UNEXPECTED_ERROR_DURING_CONNECTION_VALIDATION, e);
            }
            return false;
        }
    }

    private static StaticCredentialsProvider getCredentialsProvider (AssumeRoleResponse assumeRoleResponse) {
        AwsSessionCredentials sessionCredentials = AwsSessionCredentials.create(
                assumeRoleResponse.credentials().accessKeyId(),
                assumeRoleResponse.credentials().secretAccessKey(),
                assumeRoleResponse.credentials().sessionToken());

        return StaticCredentialsProvider.create(sessionCredentials);
    }

    public String getSecretName(String externalProviderAccountId) {
        return SECRET_KEY_PATH_AWS.concat(externalProviderAccountId);
    }

    @Override
    public boolean isDoCheckIfExternalAccountLinkedToAnotherProviderAccount() {
        return true;
    }
}
