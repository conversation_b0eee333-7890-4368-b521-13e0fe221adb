// Copyright (c) 2024 Boomi, LP
package com.boomi.aiagentregistry.service;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerAsyncClient;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;
import software.amazon.awssdk.services.secretsmanager.model.CreateSecretRequest;
import software.amazon.awssdk.services.secretsmanager.model.CreateSecretResponse;
import software.amazon.awssdk.services.secretsmanager.model.DeleteSecretRequest;
import software.amazon.awssdk.services.secretsmanager.model.DeleteSecretResponse;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueRequest;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueResponse;
import software.amazon.awssdk.services.secretsmanager.model.InvalidRequestException;
import software.amazon.awssdk.services.secretsmanager.model.RemoveRegionsFromReplicationRequest;
import software.amazon.awssdk.services.secretsmanager.model.ReplicaRegionType;
import software.amazon.awssdk.services.secretsmanager.model.ReplicateSecretToRegionsRequest;
import software.amazon.awssdk.services.secretsmanager.model.ResourceNotFoundException;
import software.amazon.awssdk.services.secretsmanager.model.SecretsManagerException;
import software.amazon.awssdk.services.secretsmanager.model.SecretsManagerResponse;
import software.amazon.awssdk.services.secretsmanager.model.UpdateSecretRequest;
import software.amazon.awssdk.services.secretsmanager.model.UpdateSecretResponse;
import com.boomi.aiagentregistry.config.AppConfig;
import com.boomi.util.StringUtil;

import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

import static com.boomi.aiagentregistry.constant.ApplicationConstant.EXCEPTION_OCCURRED;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.SECRET_EXISTS;
import static com.boomi.aiagentregistry.constant.ApplicationConstant.SECRET_NOT_EXISTS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_DELETE_REPLICATION_SECRET;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_EMPTY_SECRET_NAME;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_INVALID_REQUEST_FOR_SECRET_CHECK;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_SECRET_NOT_EXISTS;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_UPDATING_SECRET;
import static com.boomi.aiagentregistry.constant.ErrorMessages.ERROR_WHILE_CHECKING_SECRET;
import static com.boomi.aiagentregistry.constant.ErrorMessages.INFO_UPDATED_SECRET;
import static com.boomi.aiagentregistry.constant.ErrorMessages.INFO_UPDATING_SECRET;
import static com.boomi.aiagentregistry.constant.ErrorMessages.SUCCESSFUL_SECRET_DELETION;
import static com.boomi.aiagentregistry.constant.ErrorMessages.UNEXPECTED_ERROR_WHILE_CHECKING_SECRET;

@Service
@Slf4j
public class SecretsManagerServiceImpl implements SecretsManagerService {

    private final SecretsManagerClient _secretsClient;
    private final SecretsManagerAsyncClient _secreteAsyncClient;
    private final AppConfig _appConfig;

    public SecretsManagerServiceImpl(AppConfig appConfig) {
        _appConfig = appConfig;
        log.debug("AppConfig injected: {}", appConfig != null);
        _secretsClient = buildSecretsManagerClient();
        _secreteAsyncClient = buildAsyncSecretesManagerClient();
    }

    private SecretsManagerClient buildSecretsManagerClient() {
        return SecretsManagerClient.builder().region(Region.of(_appConfig.getAwsregion())).build();
    }

    private static SecretsManagerAsyncClient buildAsyncSecretesManagerClient() {
        return SecretsManagerAsyncClient.builder().region(Region.of("us-east-1")).build();
    }

    @Override
    public SecretsManagerResponse createSecret(String secretName, String secretValue) {
        try {
            CreateSecretRequest createSecretRequest = CreateSecretRequest.builder().name(String.valueOf(secretName))
                    .secretString(secretValue).build();

            CreateSecretResponse createSecretResponse = _secretsClient.createSecret(createSecretRequest);
            log.info("Secrets Manager Create Response: {}", createSecretResponse);
            // Replicate the secret to the secondary region
            List<String> replicaRegions = Arrays.asList(_appConfig.getAwssecondaryregion());
            ReplicateSecretToRegionsRequest replicateRequest = ReplicateSecretToRegionsRequest.builder().secretId(
                    createSecretResponse.name()).addReplicaRegions(
                    ReplicaRegionType.builder().region(_appConfig.getAwssecondaryregion()).build()).build();

            _secretsClient.replicateSecretToRegions(replicateRequest);
            log.info("Secret replicated to regions: {}", replicaRegions);

            return createSecretResponse;
        } catch (SecretsManagerException e) {
            log.info("Exception", e);
            return null;
        }
    }

    private String checkSecretExists(String secretName) {
        if (StringUtil.isBlank(secretName)) {
            log.warn(ERROR_EMPTY_SECRET_NAME);
            return SECRET_NOT_EXISTS;
        }

        try {
            GetSecretValueRequest getRequest = GetSecretValueRequest.builder()
                    .secretId(secretName)
                    .build();

            // This will throw ResourceNotFoundException if secret doesn't exist
            GetSecretValueResponse response = _secretsClient.getSecretValue(getRequest);

            // If we get here, the secret exists and we can access it
            if (response != null && response.name() != null) {
                log.info("Secret {} exists", secretName);
                return SECRET_EXISTS;
            }

            return SECRET_NOT_EXISTS;
        } catch (ResourceNotFoundException e) {
            // This is an expected exception when secret doesn't exist
            log.warn(ERROR_SECRET_NOT_EXISTS, secretName, e.getMessage(), e);
            return SECRET_NOT_EXISTS;
        } catch (InvalidRequestException e) {
            // Invalid parameters were provided
            log.warn(ERROR_INVALID_REQUEST_FOR_SECRET_CHECK, secretName, e.getMessage(), e);
            return EXCEPTION_OCCURRED;
        } catch (SecretsManagerException e) {
            // Handle other AWS Secrets Manager specific exceptions
            log.warn(ERROR_WHILE_CHECKING_SECRET, secretName, e.getMessage(), e);
            return EXCEPTION_OCCURRED;
        } catch (Exception e) {
            // Handle any unexpected exceptions
            log.warn(UNEXPECTED_ERROR_WHILE_CHECKING_SECRET, secretName, e.getMessage(), e);
            return EXCEPTION_OCCURRED;
        }
    }

    @Override
    public SecretsManagerResponse deleteSecret(String secretName) {

        if (StringUtil.isBlank(secretName)) {
            log.warn(ERROR_EMPTY_SECRET_NAME);
            return null;
        }
        try {
            deleteReplicaSecret(secretName);

            // Delete the secret from primary region.
            DeleteSecretRequest deleteSecretRequest = DeleteSecretRequest.builder()
                    .secretId(secretName)
                    .forceDeleteWithoutRecovery(true)
                    .build();

            DeleteSecretResponse deleteSecretResponse = _secretsClient.deleteSecret(deleteSecretRequest);
            log.info(SUCCESSFUL_SECRET_DELETION, deleteSecretResponse.name());
            return deleteSecretResponse;
        } catch (Exception e) {
            log.warn(ERROR_DELETE_REPLICATION_SECRET, secretName, e.getMessage(), e);
            return null;
        }
    }

    private void deleteReplicaSecret(String secretName) {
        try {
            // delete replication
            RemoveRegionsFromReplicationRequest deleteReplicaSecret =
                    RemoveRegionsFromReplicationRequest.builder()
                            .secretId(secretName)
                            .removeReplicaRegions(_appConfig.getAwssecondaryregion())
                            .build();

            _secretsClient.removeRegionsFromReplication(deleteReplicaSecret);
        } catch (Exception e) {
            log.error("Error occurred on deleting the secret in secondary region", e);
        }
    }

    @Override
    public Flux<String> getSecret(String secretKey) {
        return Flux.create(sink -> {
            GetSecretValueRequest request = GetSecretValueRequest.builder().secretId(secretKey).build();

            _secreteAsyncClient.getSecretValue(request).whenCompleteAsync((response, exception) -> {
                if (exception != null) {
                    log.error("Exception while fetching secret", exception);
                    sink.error(exception);
                } else {

                    sink.next(response.secretString());
                    sink.complete();
                }
            });
        });
    }

    @Override
    public UpdateSecretResponse updateSecret(String secretName, String secretValue) {
        log.info(INFO_UPDATING_SECRET);
        try {
            UpdateSecretRequest updateSecretRequest = UpdateSecretRequest.builder().secretId(secretName).secretString(
                    secretValue).build();
            UpdateSecretResponse updateSecretResponse = _secretsClient.updateSecret(updateSecretRequest);
            log.info(INFO_UPDATED_SECRET);
            log.info("Updating secret in secondary region");
            ReplicateSecretToRegionsRequest replicateRequest = ReplicateSecretToRegionsRequest.builder().secretId(
                    secretName).addReplicaRegions(
                    ReplicaRegionType.builder().region(_appConfig.getAwssecondaryregion()).build()).build();
            _secretsClient.replicateSecretToRegions(replicateRequest);
            return updateSecretResponse;
        } catch (SecretsManagerException e) {
            log.error(ERROR_UPDATING_SECRET, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public int isProviderSecretExists(String secretName) {
        // in case any exception occurred (other than ResourceNotFoundException) on checking secret exist in AWS
        // we shouldn't allow to delete provider.
        final String secretExistsResponseStr = checkSecretExists(secretName);

        // if secret exist 1
        // if secret not exist 0
        // if any exception occurred other than ResourceNotFoundException / default -1
        return switch (secretExistsResponseStr) {
            case SECRET_EXISTS -> 1;
            case SECRET_NOT_EXISTS -> 0;
            default -> -1;
        };
    }
}
