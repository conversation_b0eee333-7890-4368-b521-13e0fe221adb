// Copyright (c) 2025 Boomi, LP
package com.boomi.aiagentregistry.service.metrics;

import graphql.schema.DataFetchingEnvironment;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.repo.AiAgentProviderAccountRepository;
import com.boomi.aiagentregistry.util.UserUtil;
import com.boomi.graphql.server.schema.types.AiAgentProviderType;
import com.boomi.graphql.server.schema.types.AiAgentRegistryAgentMetric;
import com.boomi.graphql.server.schema.types.AiAgentRegistryAggregateMetrics;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiAgentRegistryGraphMetrics;
import com.boomi.graphql.server.schema.types.AiAgentRegistryMetricsErrorCode;
import com.boomi.graphql.server.schema.types.AiAgentRegistryMetricsInput;
import com.boomi.graphql.server.servlet.ErrorUtil;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.EnumSet;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class AiAgentRegistryMetricsService {

    private final TimestreamService _timestreamService;
    private final AiAgentProviderAccountRepository _aiAgentProviderAccountRepository;
    private final UserUtil _userUtil;

    @Value("${boomi.services.timestream.database_name}")
    private String _databaseName;
    @Value("${boomi.services.timestream.agent_metrics_table_name}")
    private String _agentMetricsTableName;

    private String _fromAgentMetrics;

    public static final String AGENT_EXTERNAL_ID_FIELD = "agentExternalId";
    public static final String ALIAS_EXTERNAL_ID_FIELD = "aliasExternalId";
    public static final String PROVIDER_ACCOUNT_GUID_FIELD = "providerAccountGuId";
    public static final String PROVIDER_TYPE_FIELD = "providerType";
    public static final String AI_AGENT_LISTING_FIELD = "aiAgentListing";
    private static final int ZERO_COUNT = 0;

    private static final String SELECT_GRAPH_METRICS = """
        SELECT\s
            bin(time, <BIN_DURATION>) AS bucketTs,
            COUNT(DISTINCT ExtAgentId) as activeAgents,
            COUNT(*) AS totalInvocations,
            SUM(ModelInvocationCount) AS totalModelInvocations,
           \s
            ROUND(AVG(CAST(TotalTime AS DOUBLE)), 2) AS averageTimePerInvocation,
           \s
            SUM(InputTokenCount) + SUM (OutputTokenCount) AS totalTokens,
            SUM(InputTokenCount) AS totalInputTokens,
            SUM(OutputTokenCount) AS totalOutputTokens,
            ROUND(AVG(CAST(InputTokenCount AS DOUBLE)), 2) AS avgInputTokens,
            ROUND(AVG(CAST(OutputTokenCount AS DOUBLE)), 2) AS avgOutputTokens,
            SUM(InvocationServerErrors) AS totalInvocationServerErrors,
            SUM(InvocationClientErrors) AS totalInvocationClientErrors,
           \s
            SUM(InvocationThrottles) AS totalInvocationThrottles
    """;

    private static final String SELECT_AGGREGATE_METRICS = """
            SELECT\s
               COUNT(DISTINCT ExtAgentId) as activeAgents,
               SUM(InputTokenCount) + SUM(OutputTokenCount) AS totalTokens,
               ROUND(AVG(CAST(TotalTime AS DOUBLE)), 2) as avgResponseTime,
               SUM(InvocationServerErrors) + SUM(InvocationClientErrors) + SUM(ModelInvocationUnclassifiedErrors)
               AS totalErrors,
               COUNT(*) as totalInvocations,
               ROUND(AVG(CAST(ModelLatency AS DOUBLE)), 2) as avgModelLatency,
               ROUND(AVG(CAST(InvocationThrottles AS DOUBLE)), 2) as avgInvocationThrottles,
               -- Success rate: count of rows where both error types are 0
               ROUND(
                   (SUM(CASE\s
                       WHEN InvocationServerErrors = 0 
                       AND InvocationClientErrors = 0 
                       AND ModelInvocationUnclassifiedErrors = 0 THEN 1\s
                       ELSE 0\s
                   END) * 100.0 / NULLIF(COUNT(*), 0)),
                   2
               ) as successRate,
               -- Error rate: count of rows where either error type is > 0
               ROUND(
                   (SUM(CASE\s
                       WHEN InvocationServerErrors > 0 
                       OR InvocationClientErrors > 0 
                       OR ModelInvocationUnclassifiedErrors > 0 THEN 1\s
                       ELSE 0\s
                   END) * 100.0 / NULLIF(COUNT(*), 0)),
                   2
               ) as errorRate
    """;

    private static final String SELECT_INVOCATIONS_METRICS = """
        SELECT\s
               ExtAgentAliasId as aliasExternalId,
               ExtAgentId as agentExternalId,
               ProviderType as providerType,
               ProviderAccountId as providerAccountGuId,
               COUNT(*) as totalInvocations,
               ROUND(AVG(CAST(TotalTime AS DOUBLE)), 2) as averageTime
    """;

    private static final String WHERE_TIME = """
            time >= '%s' AND time < '%s'
    """;

    private static final String WHERE_PROVIDER_ACCOUNT_ID_IN = """
                AND ProviderAccountId IN (%s)
    """;

    private static final String WHERE_IDP_ACCOUNT_ID_EQ = """
                AND AccountId = '%s'
    """;

    private static final String WHERE_PROVIDER_TYPE_IN = """
                AND ProviderType IN (%s)
    """;

    private static final String WHERE_EXT_MODEL_ID_IN = """
                AND ExtModelId IN (%s)
    """;

    private static final String WHERE_EXT_AGENT_ID_IS_NOT_NULL = """
                AND ExtAgentId IS NOT NULL
    """;

    private static final String GROUP_BY_BUCKET_TIME = """
        GROUP BY\s
            bin(time, <BIN_DURATION>)
    """;

    private static final String ORDER_BY_BUCKET_TIME = """
        ORDER BY\s
            bucketTs asc
    """;

    private static final String GROUP_BY_EXT_ALIAS_ID_EXT_AGENT_ID_PROVIDER_TYPE = """
        GROUP BY\s
                 ExtAgentAliasId, ExtAgentId, ProviderType, ProviderAccountId
    """;

    private static final String ORDER_BY_EXT_AGENT_ID_ASC = """
        ORDER BY\s
            UPPER(ExtAgentId) asc
    """;

    private static final String BIN_DURATION = "<BIN_DURATION>";
    private static final String WHERE = " WHERE ";
    private static final String INVALID_TIME = "End time {} is not less than start time {}";
    private static final String COMMA_SEPARATOR = ", ";
    private static final String SINGLE_QUOTE = "'";
    private static final String LIMIT_ROWS = " LIMIT 1000";
    private static final int SINGLE_INPUT = 1;

    private static final EnumSet<AiAgentProviderType> DEFAULT_PROVIDER_TYPES =
            EnumSet.complementOf(EnumSet.of(AiAgentProviderType.CUSTOM));

    @PostConstruct
    public void init() {
        _fromAgentMetrics = String.format("""
               FROM\s
                   "%s"."%s"
           """, _databaseName, _agentMetricsTableName);
    }

    public CompletableFuture<List<AiAgentRegistryGraphMetrics>> getGraphMetrics(AiAgentRegistryMetricsInput input,
            DataFetchingEnvironment dfe) {

        if (!validateInput(input.getRequestTsStart(), input.getRequestTsEnd(), dfe)) {
            return CompletableFuture.completedFuture(null);
        }

        String accountFilter;

        if (isEmptyProviderIdsInput(input, dfe)) {
            if (ErrorUtil.hasErrors(dfe)) {
                return CompletableFuture.completedFuture(null);
            }
            accountFilter = constructIdpAccountIdFilter(dfe);
        } else {
            Set<String> providerAccountIds = validateProviderAccountIds(input, dfe);
            if (providerAccountIds.isEmpty()) {
                return CompletableFuture.completedFuture(null);
            }
            accountFilter = constructProviderAccountIdsFilter(providerAccountIds);
        }

        String graphMetricsSql = getGraphMetricsSql(input, accountFilter);

        if (graphMetricsSql.isEmpty()) {
            return CompletableFuture.completedFuture(null);
        }

        CompletableFuture<List<AiAgentRegistryGraphMetrics>> completion = new CompletableFuture<>();

        _timestreamService.execute(graphMetricsSql,
                (rowValues, columnInfos) -> _timestreamService.mapTimestreamRowToType(rowValues, columnInfos, dfe,
                        completion, AiAgentRegistryGraphMetrics.class, false),
                (aiAgentRegistryGraphMetrics, executionException) -> _timestreamService.processTimestreamCompletionList(
                        graphMetricsSql, aiAgentRegistryGraphMetrics, executionException, dfe, completion));

        return completion;
    }

    public CompletionStage<AiAgentRegistryAggregateMetrics> getAggregateMetrics(
            @NotNull AiAgentRegistryMetricsInput input, DataFetchingEnvironment dfe) {

        if (!validateInput(input.getRequestTsStart(), input.getRequestTsEnd(), dfe)) {
           return CompletableFuture.completedFuture(null);
        }

        String accountFilter;

        if (isEmptyProviderIdsInput(input, dfe)) {
            if (ErrorUtil.hasErrors(dfe)) {
                return CompletableFuture.completedFuture(null);
            }
            accountFilter = constructIdpAccountIdFilter(dfe);
        } else {
            Set<String> providerAccountIds = validateProviderAccountIds(input, dfe);
            if (providerAccountIds.isEmpty()) {
                return CompletableFuture.completedFuture(null);
            }
            accountFilter = constructProviderAccountIdsFilter(providerAccountIds);
        }

        String aggregateMetricsSql = getAggregateMetricsSql(input, accountFilter);

        if (aggregateMetricsSql.isEmpty()) {
           return CompletableFuture.completedFuture(null);
        }

        CompletableFuture<AiAgentRegistryAggregateMetrics> completion = new CompletableFuture<>();

        _timestreamService.execute(aggregateMetricsSql,
               (rowValues, columnInfos) ->
                       _timestreamService.mapTimestreamRowToType(rowValues, columnInfos, dfe,
                               completion, AiAgentRegistryAggregateMetrics.class, false),
               (aiAgentRegistryGraphMetrics, executionException) ->
                       _timestreamService.processTimestreamCompletionSingle(aggregateMetricsSql,
                               aiAgentRegistryGraphMetrics, executionException, dfe, completion));

        return completion;
    }

    private static boolean validateInput(Date startTs, Date endTs, DataFetchingEnvironment dfe) {
        long endTime = endTs.getTime();
        long startTime = startTs.getTime();
        if (endTime <= startTime) {
            log.info(INVALID_TIME, endTs, startTs);
            ErrorUtil
                    .addError(dfe, AiAgentRegistryMetricsErrorCode.START_TS_INPUT_NOT_LESS_THAN_END_TS, endTs, startTs);
            return false;
        }

        return true;
    }

    private String getGraphMetricsSql(AiAgentRegistryMetricsInput input, String accountFilter) {

        String binDuration = TimestreamIntervalCalculator.calculateTimestreamInterval
                (input.getRequestTsStart(), input.getRequestTsEnd());
        log.info("Bin duration is {}", binDuration);

        return SELECT_GRAPH_METRICS.replace(BIN_DURATION, binDuration) +
                _fromAgentMetrics +
                WHERE +
                getWhereClause(input, accountFilter) +
                GROUP_BY_BUCKET_TIME.replace(BIN_DURATION, binDuration) +
                ORDER_BY_BUCKET_TIME +
                LIMIT_ROWS;
    }

    private String getAggregateMetricsSql(AiAgentRegistryMetricsInput input, String accountFilter) {

        return SELECT_AGGREGATE_METRICS +
                _fromAgentMetrics +
                WHERE +
                getWhereClause(input, accountFilter) +
                LIMIT_ROWS;
     }

    private String getInvocationsMetricsSql(AiAgentRegistryMetricsInput input, String accountFilter) {

        return SELECT_INVOCATIONS_METRICS +
                _fromAgentMetrics +
                WHERE +
                getWhereClause(input, accountFilter) +
                WHERE_EXT_AGENT_ID_IS_NOT_NULL +
                GROUP_BY_EXT_ALIAS_ID_EXT_AGENT_ID_PROVIDER_TYPE +
                ORDER_BY_EXT_AGENT_ID_ASC +
                LIMIT_ROWS;
    }

    private @NotNull String getWhereClause(AiAgentRegistryMetricsInput input, String accountFilter) {

        String providerTypeFilter = constructProviderTypeFilter(input);

        String modelFilter = constructModelIdFilter(input);

        String timeFilter = constructTimeFilter(input);

        // Combine all filters into where clause
        return (timeFilter + accountFilter + providerTypeFilter + modelFilter);
    }

    private boolean isEmptyProviderIdsInput(AiAgentRegistryMetricsInput input, DataFetchingEnvironment dfe) {
        List<String> inputIds = input.getProviderAccountIds();
        if (inputIds != null && !inputIds.isEmpty()) {
            return false;
        }

        int providerAccountsCountByIdpAccountId = _aiAgentProviderAccountRepository
                .findProviderAccountsCountByIdpAccountId(_userUtil.getAccountId(dfe));

        if (providerAccountsCountByIdpAccountId == ZERO_COUNT) {
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.NO_PROVIDER_ACCOUNTS_FOUND);
        }

        return true;
    }

    private Set<String> validateProviderAccountIds(AiAgentRegistryMetricsInput input, DataFetchingEnvironment dfe) {
        List<String> inputIds = input.getProviderAccountIds();

        Set<String> validIds = new HashSet<>(inputIds.size());
        for (String id : inputIds) {
            if (!isValidUUID(id, dfe)) {
                return Collections.emptySet();
            }
            validIds.add(id);
        }

        String idpAccountId = _userUtil.getAccountId(dfe);
        boolean allExist = _aiAgentProviderAccountRepository
                .getProviderAccountsCountByAccountGuidsAndIdpAccountId(validIds, idpAccountId) == validIds.size();

        if (!allExist) {
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.PROVIDER_ACCOUNT_ID_NOT_FOUND);
            return Collections.emptySet();
        }

        return validIds;
    }

    private static boolean isValidUUID(String id, DataFetchingEnvironment dfe) {
        try {
            UUID.fromString(id);
            return true;
        } catch (IllegalArgumentException e) {
            log.error("Invalid Provider Account ID: {}", id, e);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.INVALID_PROVIDER_ACCOUNT_ID, id);
            return false;
        }
    }


    private static Set<String> validateStringArrayInput(List<String> stringArrayInput) {
        return Optional.ofNullable(stringArrayInput)
                .map(set -> set.stream().map(Object::toString).collect(Collectors.toSet()))
                .filter(ids -> !(ids.size() == SINGLE_INPUT && ids.contains(StringUtils.EMPTY)))
                .orElse(Collections.emptySet());
    }

    private static String constructProviderAccountIdsFilter(Set<String> providerAccountIds) {
        return String.format(WHERE_PROVIDER_ACCOUNT_ID_IN, getCommaSeparatedString(providerAccountIds));
    }

    private String constructIdpAccountIdFilter(DataFetchingEnvironment dfe) {
        return String.format(WHERE_IDP_ACCOUNT_ID_EQ, _userUtil.getAccountId(dfe));
    }

    private static String constructModelIdFilter(AiAgentRegistryMetricsInput input) {
        Set<String> modelIds = validateStringArrayInput(input.getModelIds());
        return (modelIds == null || modelIds.isEmpty())
                ? StringUtils.EMPTY
                : String.format(WHERE_EXT_MODEL_ID_IN, getCommaSeparatedString(modelIds));
    }

    private static String constructProviderTypeFilter(AiAgentRegistryMetricsInput input) {
        EnumSet<AiAgentProviderType> providerAccountTypes = validateProviderTypesInput(input);
        return buildProviderTypeWhereClause(providerAccountTypes.isEmpty()
                ? DEFAULT_PROVIDER_TYPES
                : providerAccountTypes);
    }

    private static String buildProviderTypeWhereClause(Set<AiAgentProviderType> providerTypes) {
        String joinedProviderTypes = providerTypes.stream()
                .map(aiAgentProviderType -> SINGLE_QUOTE + aiAgentProviderType.name() + SINGLE_QUOTE)
                .collect(Collectors.joining(COMMA_SEPARATOR));
        return String.format(WHERE_PROVIDER_TYPE_IN, joinedProviderTypes);
    }

    private static EnumSet<AiAgentProviderType> validateProviderTypesInput(AiAgentRegistryMetricsInput input) {
        return Optional.ofNullable(input.getProviderTypes())
                .filter(types -> !types.isEmpty())
                .map(EnumSet::copyOf)
                .orElseGet(() -> EnumSet.noneOf(AiAgentProviderType.class));
    }

    private String constructTimeFilter(AiAgentRegistryMetricsInput input) {
        return String.format(WHERE_TIME,
                _timestreamService.formatTimestreamTimeFilter(input.getRequestTsStart()),
                _timestreamService.formatTimestreamTimeFilter(input.getRequestTsEnd()));
    }

    private static String getCommaSeparatedString(Set<String> ids) {
        return ids.stream()
                .map(id -> SINGLE_QUOTE + id + SINGLE_QUOTE)
                .collect(Collectors.joining(COMMA_SEPARATOR));
    }

    public CompletionStage<List<AiAgentRegistryAgentMetric>> getInvocationsMetrics(
            @NotNull AiAgentRegistryMetricsInput input, DataFetchingEnvironment dfe) {

        if (!validateInput(input.getRequestTsStart(), input.getRequestTsEnd(), dfe)) {
            return CompletableFuture.completedFuture(null);
        }

        String accountFilter;

        if (isEmptyProviderIdsInput(input, dfe)) {
            if (ErrorUtil.hasErrors(dfe)) {
                return CompletableFuture.completedFuture(null);
            }
            accountFilter = constructIdpAccountIdFilter(dfe);
        } else {
            Set<String> providerAccountIds = validateProviderAccountIds(input, dfe);
            if (providerAccountIds.isEmpty()) {
                return CompletableFuture.completedFuture(null);
            }
            accountFilter = constructProviderAccountIdsFilter(providerAccountIds);
        }

        String invocationsMetricsSql = getInvocationsMetricsSql(input, accountFilter);

        if (invocationsMetricsSql.isEmpty()) {
            return CompletableFuture.completedFuture(null);
        }

        CompletableFuture<List<AiAgentRegistryAgentMetric>> completion = new CompletableFuture<>();

        _timestreamService.execute(invocationsMetricsSql,
                (rowValues, columnInfos) ->
                        _timestreamService.mapTimestreamRowToType(rowValues, columnInfos, dfe,
                                completion, AiAgentRegistryAgentMetric.class, true),
                (aiAgentInvocationsMetrics, executionException) ->
                        _timestreamService.processTimestreamCompletionList(invocationsMetricsSql,
                        aiAgentInvocationsMetrics, executionException, dfe, completion));

        return completion;
    }
}
