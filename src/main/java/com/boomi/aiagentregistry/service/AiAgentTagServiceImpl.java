// Copyright (c) 2024 Boomi, LP.
package com.boomi.aiagentregistry.service;

import graphql.schema.DataFetchingEnvironment;
import lombok.extern.slf4j.Slf4j;
import com.boomi.aiagentregistry.constant.ErrorMessages;
import com.boomi.aiagentregistry.entity.AiAgent;
import com.boomi.aiagentregistry.entity.AiAgentTag;
import com.boomi.aiagentregistry.entity.AiAgentTagAssociation;
import com.boomi.aiagentregistry.exception.AiAgentRegistryException;
import com.boomi.aiagentregistry.repo.AiAgentTagAssociationRepository;
import com.boomi.aiagentregistry.repo.AiAgentTagRepository;
import com.boomi.aiagentregistry.util.GuidUtil;
import com.boomi.aiagentregistry.util.UserUtil;
import com.boomi.graphql.server.schema.types.AiAgentRegistryErrorCode;
import com.boomi.graphql.server.schema.types.AiAgentTAgMutationDataOutput;
import com.boomi.graphql.server.schema.types.AiAgentTagInput;
import com.boomi.graphql.server.schema.types.AiAgentTagMutationDataInput;
import com.boomi.graphql.server.schema.types.AiAgentTagMutationInput;
import com.boomi.graphql.server.schema.types.AiAgentTagMutationOutput;
import com.boomi.graphql.server.schema.types.AiAgentTagQueryResponse;
import com.boomi.graphql.server.schema.types.AiRegistryEntityType;
import com.boomi.graphql.server.servlet.ErrorUtil;

import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.PessimisticLockingFailureException;
import org.springframework.dao.QueryTimeoutException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AiAgentTagServiceImpl implements IAiAgentTagService{

    private static final Lock aiAgentTagLock = new ReentrantLock();

    private static final long LOCK_TIMEOUT_SECONDS = 10;

    private final UserUtil _userUtil;

    private final AiAgentTagRepository _aiAgentTagRepository;

    private final AiAgentTagAssociationRepository _aiAgentTagAssociationRepository;

    private final Map<String, ReferenceEntityManager> _referenceEntityManagerMap;

    public AiAgentTagServiceImpl(UserUtil userUtil, AiAgentTagRepository aiAgentTagRepository,
            AiAgentTagAssociationRepository aiAgentTagAssociationRepository,
            Map<String, ReferenceEntityManager> referenceEntityManagerMap) {
        _userUtil = userUtil;
        _aiAgentTagRepository = aiAgentTagRepository;
        _aiAgentTagAssociationRepository = aiAgentTagAssociationRepository;
        _referenceEntityManagerMap = Collections.unmodifiableMap(referenceEntityManagerMap);
    }

    @Override
    public CompletableFuture<AiAgentTagQueryResponse> getAiAgentTags(DataFetchingEnvironment dfe) {
        CompletableFuture<AiAgentTagQueryResponse> completion = new CompletableFuture<>();
        AiAgentTagQueryResponse aiAgentTagQueryResponse = new AiAgentTagQueryResponse();
        try {
            String idpAccountId = _userUtil.getAccountId(dfe);

            List<String> tagKeys = _aiAgentTagRepository
                    .findByIdpAccountIdFromMaterializedView(idpAccountId);

            aiAgentTagQueryResponse.setTags(tagKeys);
            completion.complete(aiAgentTagQueryResponse);
            return completion;

        }catch (Exception e) {
            log.error(ErrorMessages.ERROR_FETCHING_AI_AGENT_TAGS, e);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.FETCH_AI_AGENT_TAG_ERROR);
            completion.complete(null);
        }
        return completion;
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public CompletionStage<AiAgentTagMutationOutput> aiAgentAddTags(AiAgentTagMutationInput input,
            DataFetchingEnvironment dfe) {
        CompletableFuture<AiAgentTagMutationOutput> completion = new CompletableFuture<>();
        AiAgentTagMutationOutput response = new AiAgentTagMutationOutput();
        try {
            if (isInvalidInput(input)) {
                response.setTags(null);
                completion.complete(response);
                ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.INVALID_TAG_INPUT);
                log.warn(AiAgentRegistryErrorCode.INVALID_TAG_INPUT.getDetail(), dfe);
                return completion;
            }

            List<AiAgentTagMutationDataInput> tagToSync = input.getTags();

            String idpAccountId = _userUtil.getAccountId(dfe);

            // Collect all tag keys from input
            Set<String> inputTagKeys = tagToSync.stream()
                    .flatMap(sync -> sync.getTags().stream())
                    .map(value -> value.getKey().toLowerCase())
                    .collect(Collectors.toSet());

            List<AiAgentTAgMutationDataOutput> dataOutputs = new ArrayList<>();
            AiAgentTAgMutationDataOutput output;

            // Process all tags at once
            for (AiAgentTagMutationDataInput tagSync : tagToSync) {
                output = processTagSync(tagSync, inputTagKeys, idpAccountId, dfe);
                dataOutputs.add(output);
            }

            response.setTags(dataOutputs);
            completion.complete(response);
        }catch(InterruptedException e){
            Thread.currentThread().interrupt();
            log.error(ErrorMessages.ERROR_THREAD_INTERRUPTION_DURING_SYNC, e);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.SYNC_AI_AGENT_TAG_ERROR);
        }
        catch (Exception e) {
            log.error(ErrorMessages.TAG_SYNC_ERROR, e);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.SYNC_AI_AGENT_TAG_ERROR);
            response.setTags(null);
            completion.complete(response);
        }

        return completion;
    }


    private AiAgentTAgMutationDataOutput processTagSync(AiAgentTagMutationDataInput tagSync,
            Set<String> inputTagKeys,
            String idpAccountId, DataFetchingEnvironment dfe) throws InterruptedException, TimeoutException {
        try {
            if (aiAgentTagLock.tryLock(LOCK_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {

                AiAgentTAgMutationDataOutput output = new AiAgentTAgMutationDataOutput();

                // Get agent details
                AiRegistryEntityType relatedEntityType = tagSync.getRelatedEntityType();
                String entityId = tagSync.getRelatedEntityRefId().getId();

                ReferenceEntityManager referenceEntityManager = _referenceEntityManagerMap
                        .get(relatedEntityType.toString());
                AiAgent agent = referenceEntityManager.getAgent(entityId);
                if (agent != null) {

                    Integer relatedEntityUid = referenceEntityManager.getRelatedEntityUid(entityId);

                    List<AiAgentTagInput> uniqueTags = new ArrayList<>(
                            tagSync.getTags().stream()
                                    .collect(Collectors.toMap(
                                            tag -> tag.getKey().toLowerCase(),
                                            tag -> {
                                                tag.setKey(tag.getKey().toLowerCase());
                                                return tag;
                                            },
                                            (existing, duplicate) -> existing)).values()
                    );

                    // Get all existing tags for this idpAccountId in one query
                    Map<String, AiAgentTag> existingTagMap = _aiAgentTagRepository
                            .findByIdpAccountIdAndTagKeyInWithLock(idpAccountId, inputTagKeys)
                            .stream()
                            .collect(Collectors.toMap(
                                    tag -> generateKey(tag.getKey(), idpAccountId),
                                    tag -> tag,
                                    (existing, replacement) -> existing));

                    // Process and save tags
                    handleTags(existingTagMap, idpAccountId,uniqueTags);

                    // Process and save association
                    handleAssociation(existingTagMap, idpAccountId, relatedEntityType, relatedEntityUid, uniqueTags);

                    //Post association if all success create response object.

                    List<AiAgentTag> savedTagsForRelatedEntityRefIdAndEntityType = _aiAgentTagAssociationRepository
                            .findAiAgentTagFromAiAgentTagAssociationByRelatedEntityTypeAndRelatedEntityIdWithLock
                                    (relatedEntityType, relatedEntityUid);

                    List<com.boomi.graphql.server.schema.types.AiAgentTag> graphqlAiAgentTags =
                            savedTagsForRelatedEntityRefIdAndEntityType.stream()
                                    .map(AiAgentTagServiceImpl::getAiAgentTag)
                                    .toList();

                    output.setRelatedEntityRefId(entityId);
                    output.setRelatedEntityType(relatedEntityType);
                    output.setTags(graphqlAiAgentTags);
                } else {
                    log.warn(ErrorMessages.ERROR_FETCHING_AGENT_FROM_REF_ID_FOR_TAGS, entityId);
                    output.setRelatedEntityRefId(entityId);
                    output.setRelatedEntityType(relatedEntityType);
                    output.setTags(null);
                }

                return output;
            } else {
                throw new TimeoutException(ErrorMessages.ERROR_UNABLE_TO_ACQUIRE_LOCK);
            }
        }catch (Exception e) {
            log.error(ErrorMessages.ERROR_PROCESSING_TAG_FOR_REF_ENTITY,
                    tagSync.getRelatedEntityRefId().getId(), e);
            ErrorUtil.addError(dfe, AiAgentRegistryErrorCode.SYNC_AI_AGENT_TAG_ERROR);
            throw e;
        } finally {
            aiAgentTagLock.unlock();
        }
    }

    private void handleTags(Map<String, AiAgentTag> existingTagMap,
            String idpAccountId, List<AiAgentTagInput> uniqueTags) {
        List<AiAgentTag> tagsToSave = new ArrayList<>();

        for (AiAgentTagInput tagInput : uniqueTags) {
            String tagKey = generateKey(tagInput.getKey(), idpAccountId);
            if (!existingTagMap.containsKey(tagKey)) {
                AiAgentTag newTag = AiAgentTag.builder()
                        .guid(GuidUtil.createAIAgentTagGuid())
                        .key(tagInput.getKey())
                        .value(tagInput.getValue())
                        .idpAccountId(idpAccountId)
                        .build();
                tagsToSave.add(newTag);
            }
        }
        try{
            // Save tags and update map
            if (!tagsToSave.isEmpty()) {
                List<AiAgentTag> savedTags = _aiAgentTagRepository.saveAll(tagsToSave);
                for (AiAgentTag savedTag : savedTags) {
                    String savedTagKey = generateKey(savedTag.getKey(), idpAccountId);
                    existingTagMap.put(savedTagKey, savedTag);
                }
            }
        }catch(DataIntegrityViolationException e){
            log.warn(ErrorMessages.EXCEPTION_DATA_INTEGRITY_VIOLATION,
                    idpAccountId, e.getMessage());
        }catch (PessimisticLockingFailureException e) {
            log.warn(ErrorMessages.EXCEPTION_PESSIMISTIC_LOCKING_FAILURE, idpAccountId, e.getMessage());
        }catch (QueryTimeoutException e) {
            log.warn(ErrorMessages.EXCEPTION_QUERY_TIMEOUT, idpAccountId, e.getMessage());
        }
    }

    private void handleAssociation(Map<String, AiAgentTag> existingTagMap, String idpAccountId,
            AiRegistryEntityType relatedEntityType, Integer relatedEntityUid, List<AiAgentTagInput> uniqueTags) {
        try{
            // Get existing associations for this entity
            Set<Integer> existingTagUids = _aiAgentTagAssociationRepository
                    .findByRelatedEntityTypeAndRelatedEntityIdWithLock(relatedEntityType, relatedEntityUid)
                    .stream()
                    .map(AiAgentTagAssociation::getTagUid)
                    .collect(Collectors.toSet());

            // After getting existing associations and before creating new ones
            Set<Integer> tagUidsToKeep = uniqueTags.stream()
                    .map(tagInput -> existingTagMap.get(generateKey(tagInput.getKey(), idpAccountId)))
                    .filter(Objects::nonNull)
                    .map(AiAgentTag::getUid)
                    .collect(Collectors.toSet());

            // Find associations to remove (tags that exist but are not in the new input)
            List<AiAgentTagAssociation> associationsToRemove = _aiAgentTagAssociationRepository
                    .findByRelatedEntityTypeAndRelatedEntityIdWithLock(relatedEntityType, relatedEntityUid)
                    .stream()
                    .filter(association -> !tagUidsToKeep.contains(association.getTagUid()))
                    .toList();

            // Delete the associations that are no longer needed
            if (!associationsToRemove.isEmpty()) {
                _aiAgentTagAssociationRepository.deleteAll(associationsToRemove);
                _aiAgentTagAssociationRepository.flush();
            }

            // Create and save associations
            List<AiAgentTagAssociation> associationsToSave = new ArrayList<>();

            for (AiAgentTagInput tagInput : uniqueTags) {
                AiAgentTag savedTag = existingTagMap
                        .get(generateKey(tagInput.getKey(), idpAccountId));
                if (savedTag != null && !existingTagUids.contains(savedTag.getUid())) {
                    AiAgentTagAssociation association = AiAgentTagAssociation.builder()
                            .guid(GuidUtil.createAIAgentTagAssociationGuid())
                            .tagUid(savedTag.getUid())
                            .relatedEntityType(relatedEntityType)
                            .relatedEntityUid(relatedEntityUid)
                            .build();
                    associationsToSave.add(association);
                }
            }

            // Save associations
            if (!associationsToSave.isEmpty()) {
                _aiAgentTagAssociationRepository.saveAll(associationsToSave);
                _aiAgentTagAssociationRepository.flush();
            }
        }catch (PessimisticLockingFailureException e) {
            log.warn(ErrorMessages.EXCEPTION_PESSIMISTIC_LOCKING_FAILURE, idpAccountId, e.getMessage());

        }catch (QueryTimeoutException e) {
            log.warn(ErrorMessages.EXCEPTION_QUERY_TIMEOUT, idpAccountId, e.getMessage());
        }
    }

    private static com.boomi.graphql.server.schema.types.AiAgentTag
            getAiAgentTag(com.boomi.aiagentregistry.entity.AiAgentTag aiAgentTag) {
                com.boomi.graphql.server.schema.types.AiAgentTag
                        graphqlAiAgentTag = new com.boomi.graphql.server.schema.types.AiAgentTag();
                graphqlAiAgentTag.withId(aiAgentTag.getGuid());
                graphqlAiAgentTag.withKey(aiAgentTag.getKey());
                graphqlAiAgentTag.withValue(aiAgentTag.getValue());
                return graphqlAiAgentTag;
            }

    private static boolean isInvalidInput(AiAgentTagMutationInput input) {
        return input == null || input.getTags().isEmpty();
    }

    String generateKey(String key, String idpAccountId) {
        if (key == null || idpAccountId == null) {
            throw new IllegalArgumentException(ErrorMessages.ERROR_KEY_GENERATION_PARAMETERS_FOR_TAGS);
        }
        return String.format("%s:%s", key, idpAccountId);
    }


}
