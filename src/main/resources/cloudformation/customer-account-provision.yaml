AWSTemplateFormatVersion: '2010-09-09'
Description: >-
  The CloudFormation template enables cross-account access between the Boomi Agent Control Tower and your AWS account, 
  to manage and monitor your Amazon Bedrock agents. The template creates an IAM role with the necessary permissions 
  to invoke Bedrock APIs and retrieve agent metadata.
  
  Additionally, it establishes sharing of CloudWatch metrics, through AWS Cloudwatch Observability Access Manager (OAM).
  This enables centralized monitoring of your Bedrock agents.
  
  The template must be deployed in your AWS account to grant secure and controlled access to the Agent Control Tower 
  to manage your Bedrock agents.

Parameters:
  ExternalId:
    Type: String
    Description: "Unique identifier to secure cross-account role assumption"
    NoEcho: true

Mappings:
  ACTVariables:
    Configuration:
      ServiceAccountId: "%ACT_SERVICE_ACCOUNT_ID%"
      ServiceRoleName: "%ACT_SERVICE_ROLE_NAME%"
      CustomerPolicyName: "%ACT_CUSTOMER_POLICY_NAME%"
      CustomerRoleName: "%ACT_CUSTOMER_ROLE_NAME%"
      OamSinkArn: "%ACT_OAM_SINK_ARN%"

Conditions:
  # Skip OAM Link creation in the ACT service account to prevent self-monitoring
  SkipMonitoringAccount: !Not [!Equals [!Ref 'AWS::AccountId', !FindInMap [ACTVariables, Configuration, ServiceAccountId]]]

Resources:
  # IAM role that ACT service will assume to manage Bedrock agents in customer account
  BedrockCustomerRole:
    Type: AWS::IAM::Role
    Properties:
      # Role name is fully determined by ACT during template generation
      RoleName: !FindInMap [ACTVariables, Configuration, CustomerRoleName]
      Description: "Role for cross-account Bedrock access for ACT customer"
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              # ACT service account role that will assume this role
              AWS: !Sub
                - 'arn:aws:iam::${AccountId}:role/${RoleName}'
                - AccountId: !FindInMap [ACTVariables, Configuration, ServiceAccountId]
                  RoleName: !FindInMap [ACTVariables, Configuration, ServiceRoleName]
            Action: sts:AssumeRole
            Condition:
              StringEquals:
                # Using the ExternalId parameter for enhanced security
                sts:ExternalId: !Ref ExternalId

  # Policy granting necessary Bedrock permissions to the customer role
  BedrockAccessPolicy:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: !FindInMap [ACTVariables, Configuration, CustomerPolicyName]
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Action:
              - "bedrock:*"
              - "bedrock-runtime:*"
              - "bedrock-agent:*"
              - "bedrock-agent-runtime:*"
            Resource: "*"
      Roles:
        - !Ref BedrockCustomerRole

  # OAM Link for sharing CloudWatch metrics with ACT monitoring account
  Link:
    Type: AWS::Oam::Link
    Condition: SkipMonitoringAccount
    Properties:
      LabelTemplate: "${AWS::AccountId}"
      LinkConfiguration:
        MetricConfiguration:
          Filter: "Namespace IN ('AWS/Bedrock/Agents')"
      ResourceTypes:
        - "AWS::CloudWatch::Metric"
      # Pre-configured OAM sink in ACT account for collecting metrics
      SinkIdentifier: !FindInMap [ACTVariables, Configuration, OamSinkArn]

Outputs:
  # Output the role ARN for reference
  RoleArn:
    Description: "ARN of the role created in the customer's account"
    Value: !GetAtt BedrockCustomerRole.Arn
