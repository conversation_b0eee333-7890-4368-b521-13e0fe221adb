config:
  ai_agent_registry_boomi_contact: "Customer Journey"
  ai_agent_registry_environment: "production"
  ai_agent_registry_env_ssm: "Production"
  ai_agent_registry_stackName: "ai-agent-registry"
  ai_agent_registry_image_uri: "871876053574.dkr.ecr.us-east-1.amazonaws.com/boomi/ai-agent-registry:latest"
  ai_agent_registry_aws_region: "us-east-1"
  ai_agent_registry_aws_secondary_region: "us-west-2"
  ai_agent_registry_spring_datasource_url: "**************************************************************************************************************************************"
  ai_agent_registry_spring_datasource_username: "postgres"
  ai_agent_registry_spring_datasource_default_url: "*********************************************************************************************************************"
  ai_agent_registry_NEW_RELIC_APP_NAME: "ai-agent-registry-production"
  ai_agent_registry_newrelic_secret_name: "newrelic_secrets"
  ai_agent_registry_newrelic_api_key: "25d3f215c8219980796ba0f462d8351fFFFFNRAL"
  ai_agent_registry_newrelic_enabled: true
  ai_agent_registry_newrelic_forward_enabled: true
  ai_agent_registry_jwks_url: "https://platform.boomi.com/auth/.well-known/jwks.json"
  ai_agent_registry_jwks_issuer: "https://platform.boomi.com"
  ai_agent_registry_platformBaseUrl: "https://platform.boomi.com"
  ai_agent_registry_apiPlatformUrl: "https://platform.boomi.com"
  ai_agent_registry_monitor_user: "admin"
  ai_agent_registry_private_ipv4_addresses: "***********, ***********, ***********, ***********"
  ai_agent_registry_acm: "arn:aws:acm:us-east-1:************:certificate/81efee47-633b-4ef6-8c1e-41f2e7e94bc3"
  ai_agent_registry_externalLink_agent_aws_bedrock: "https://{REGION}.console.aws.amazon.com/bedrock/home?region={REGION}#/agents/{AGENT_ID}"
  ai_agent_registry_externalLink_agent_boomi: "https://platform.boomi.com/BoomiAI.html#ai;accountId={ACCOUNT_ID}"
  ai_agent_registry_externalLink_version_aws_bedrock: "https://{REGION}.console.aws.amazon.com/bedrock/home?region={REGION}#/agents/{AGENT_ID}/versions/{AGENT_VERSION}"
  ai_agent_registry_externalLink_version_boomi: "https://platform.boomi.com/BoomiAI.html#ai;accountId={ACCOUNT_ID}"
  ai_agent_registry_externalLink_alias_aws_bedrock: "https://{REGION}.console.aws.amazon.com/bedrock/home?region={REGION}#/agents/{AGENT_ID}/alias/{AGENT_ALIAS_ID}"
  ai_agent_registry_garden_apiUrl: "https://ai-agent-garden.datalake-prod.boomi.com"
  ai_agent_registry_garden_jwtUrl: "https://platform.boomi.com"
  boomi_services_timestream_roleArn: "arn:aws:iam::************:role/SharedTimeStreamRole-Metering-Production"
  boomi_services_timestream_database_name: "Metering-Production"
  ai_agent_registry_monitoring_account_id: "************"
  ai_agent_registry_use_BedrockAssumeRole: true
  s3_bucket: boomi-ai-agent-registry-app-data
  ai_agent_service_account_role: ai-registry-Production-ai-agent-registry-sa-role-us-east-1
  ai_agent_registry_allow_one_external_account_with_one_idp_one_auth: true
  ai_agent_registry_aws_metrics_stream_in_all_regions: true
  ai_agent_registry_use_real_oam_client: true
  ai_agent_registry_mail_enabled: true
  ai_agent_registry_mail_dl: "<EMAIL>"
  ai_agent_registry_scheduler_cron_expression: "0 0 1 * * *"
  ai_agent_registry_mail_host: "email-smtp.us-east-1.amazonaws.com"
  ai_agent_registry_mail_port: 587
