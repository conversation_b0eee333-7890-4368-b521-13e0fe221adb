config:
  ai_agent_registry_boomi_contact: "Customer Journey"
  ai_agent_registry_environment: "prodclone"
  ai_agent_registry_env_ssm: "ProdClone"
  ai_agent_registry_stackName: "ai-agent-registry"
  ai_agent_registry_image_uri: "066499543631.dkr.ecr.us-east-1.amazonaws.com/boomi/ai-agent-registry:ai-agent-registry-service-sandbox1"
  ai_agent_registry_aws_region: "us-east-1"
  ai_agent_registry_aws_secondary_region: "us-west-2"
  ai_agent_registry_spring_datasource_url: "*************************************************************************************************************************************"
  ai_agent_registry_spring_datasource_username: "postgres"
  ai_agent_registry_spring_datasource_default_url: "********************************************************************************************************************"
  ai_agent_registry_NEW_RELIC_APP_NAME: "ai-agent-registry-prodclone"
  ai_agent_registry_newrelic_secret_name: "newrelic_secrets"
  ai_agent_registry_newrelic_api_key: "25d3f215c8219980796ba0f462d8351fFFFFNRAL"
  ai_agent_registry_newrelic_enabled: true
  ai_agent_registry_newrelic_forward_enabled: true
  ai_agent_registry_jwks_url: "https://prodclone.boomi.com/auth/.well-known/jwks.json"
  ai_agent_registry_jwks_issuer: "https://prodclone.boomi.com"
  ai_agent_registry_platformBaseUrl: "https://prodclone.boomi.com"
  ai_agent_registry_apiPlatformUrl: "https://prodclone.boomi.com"
  ai_agent_registry_monitor_user: "admin"
  ai_agent_registry_private_ipv4_addresses: "***********, ***********, ***********, ***********"
  ai_agent_registry_acm: "arn:aws:acm:us-east-1:************:certificate/54919d59-9b3d-43b4-aacf-eed328c6d231"
  ai_agent_registry_externalLink_agent_aws_bedrock: "https://{REGION}.console.aws.amazon.com/bedrock/home?region={REGION}#/agents/{AGENT_ID}"
  ai_agent_registry_externalLink_agent_boomi: "https://prodclone.boomi.com/BoomiAI.html#ai;accountId={ACCOUNT_ID}"
  ai_agent_registry_externalLink_version_aws_bedrock: "https://{REGION}.console.aws.amazon.com/bedrock/home?region={REGION}#/agents/{AGENT_ID}/versions/{AGENT_VERSION_ID}"
  ai_agent_registry_externalLink_version_boomi: "https://prodclone.boomi.com/BoomiAI.html#ai;accountId={ACCOUNT_ID}"
  ai_agent_registry_externalLink_alias_aws_bedrock: "https://{REGION}.console.aws.amazon.com/bedrock/home?region={REGION}#/agents/{AGENT_ID}/alias/{AGENT_ALIAS_ID}"
  ai_agent_registry_garden_apiUrl: "https://ai-agent-garden.datalake-prodclone.boomi.com"
  ai_agent_registry_garden_jwtUrl: "https://prodclone.boomi.com"
  boomi_services_timestream_roleArn: "arn:aws:iam::************:role/SharedTimeStreamRole-Metering-ProdClone3"
  boomi_services_timestream_database_name: "Metering-ProdClone3"
  ai_agent_registry_monitoring_account_id: "************"
  ai_agent_registry_use_BedrockAssumeRole: true
  s3_bucket: boomi-ai-agent-registry-app-data
  ai_agent_service_account_role: ai-registry-ProdClone-ai-agent-registry-sa-role-us-east-1
  ai_agent_registry_allow_one_external_account_with_one_idp_one_auth: false
  ai_agent_registry_aws_metrics_stream_in_all_regions: false
  ai_agent_registry_use_real_oam_client: false
  boomi_services_aiagentregistry_sync_queue_url: "https://sqs.us-east-1.amazonaws.com/************/ai-agent-registry-prodclone-manual-sqs-queue"
  boomi_services_aiagentregistry_sync_queue_region: "us-east-1"
  ai_agent_registry_mail_enabled: true
  ai_agent_registry_mail_dl: "<EMAIL>"
  ai_agent_registry_scheduler_cron_expression: "0 0 1 * * *"
  ai_agent_registry_mail_host: "email-smtp.us-east-1.amazonaws.com"
  ai_agent_registry_mail_port: 587
